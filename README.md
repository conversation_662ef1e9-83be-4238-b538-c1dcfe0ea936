# WebRTC Simple Peer Application

A simple WebRTC client that allows two users on the same LAN to communicate using video and audio. The application uses the `simple-peer` library for WebRTC and MQTT for signaling. The application runs over HTTPS with WSS (WebSocket Secure) for enhanced security.

## Features

- **Automatic Room Joining**: Users join rooms automatically based on URL parameters
- **MQTT Signaling**: Uses MQTT broker for WebRTC peer discovery and signaling
- **Secure Communication**: HTTPS and WSS (WebSocket Secure) for all connections
- **Kiosk Mode**: Optimized for kiosk displays with fullscreen remote video and PIP local video
- **Docker Deployment**: Complete setup with MQTT broker using Docker Compose
- **Minimal Design**: Clean, focused interface showing only essential information

## Quick Start

### Docker Deployment (Recommended)

1. Build and start the containers in detached mode:
```bash
docker-compose up --build -d
```

2. Access the application:
```
https://nuri-server.local:8443/test
https://nb-probook.spot.sk:8443/test
```

3. Check container status and logs (optional):
```bash
docker-compose ps
docker-compose logs
```

## Usage

### URL Format

The application supports two URL formats:

1. **Path-based**: `/room/{roomName}/{userName}`
   - Example: `https://nuri-server.local:8443/room/meeting-room/alice`

2. **Query parameters**: `/?room={roomName}&user={userName}`
   - Example: `https://nuri-server.local:8443/?room=meeting-room&user=alice`

### Testing

1. Open the test page at `https://nuri-server.local:8443/test` or `https://nb-probook.spot.sk:8443/test`
2. Accept the self-signed certificate warning in your browser
3. Click on the first user link (e.g., Alice)
4. Allow camera and microphone access when prompted
5. Open the second user link (e.g., Bob) in a new browser window/tab
6. Allow camera and microphone access for the second user
7. The users should automatically connect via WebRTC

## Architecture

- **Frontend**: Vanilla JavaScript with simple-peer library
- **Styling**: Tailwind CSS with Flowbite components via CDN
- **Signaling**: MQTT broker (Mosquitto) for peer discovery
- **Server**: Express.js serving static files
- **Deployment**: Docker Compose with separate containers for web app and MQTT broker

## Configuration

### SSL/TLS Configuration

The application uses self-signed SSL certificates for HTTPS and WSS connections:
- **Domains**: `nuri-server.local` and `nb-probook.spot.sk`
- **Certificates**: Located in `ssl/` directory
- **Browser Warning**: You'll need to accept the self-signed certificate warning

### MQTT Broker

The application uses WSS (WebSocket Secure) for MQTT communication:
- **Secure Connection**: Uses local Mosquitto container with WSS on port 9002

### Docker Services

- **mosquitto**: MQTT broker on ports 1883 (MQTT) and 9002 (WebSocket Secure)
- **webrtc-app**: Web application on port 8443 (HTTPS)

## Files Structure

```
├── docker-compose.yml    # Docker services configuration
├── Dockerfile           # Web application container
├── mosquitto.conf       # MQTT broker configuration
├── package.json         # Node.js dependencies
├── server.js           # HTTPS Express server
├── ssl/                # SSL certificates directory
│   ├── server.crt      # SSL certificate
│   ├── server.key      # SSL private key
│   └── server.pem      # Combined certificate and key
├── public/
│   ├── index.html      # Main application page
│   ├── app.js          # WebRTC client logic
│   ├── favicon.svg     # Application icon
│   └── test.html       # Test page with example links
└── README.md           # This file
```

## Kiosk Design Features

The application is optimized for kiosk displays with:

- **Fullscreen Video Experience**:
  - Local video fullscreen when waiting for connection
  - Remote video fullscreen when connected
  - Local video becomes Picture-in-Picture (top-right) during calls
- **Minimal Icon-Only UI**:
  - Room/User info in single line (top-left) with neutral white icons
  - Pure icon status bar (bottom-right) with color-coded indicators only
  - No text status messages - completely visual interface
  - Helpful tooltips on hover for status icons with detailed explanations
  - All overlays use glassmorphism design with Tailwind CSS backdrop-blur
- **Visual Feedback**:
  - Pulsing animations during waiting states
  - Smooth transitions between video layouts
  - Status color coding (white=connected, red=disconnected, orange=connecting)
- **Kiosk Optimizations**:
  - Fullscreen video experience with minimal UI
  - No scrolling or UI interactions needed
  - Responsive design for different screen sizes
  - Custom WebRTC favicon

## Development

The application is designed to be minimal and focused. Key components:

- **WebRTC**: Handled by simple-peer library
- **Signaling**: MQTT messages for offer/answer exchange
- **UI**: Kiosk-optimized interface with fullscreen video
- **Auto-connection**: Automatic peer discovery and connection
- **State Management**: Proper cleanup when peers disconnect

## Troubleshooting

1. **SSL Certificate Warning**: Accept the self-signed certificate in your browser
2. **Camera/Microphone Access**: Ensure browser permissions are granted
3. **MQTT Connection**: Check browser console for WSS connection status
4. **Docker Issues**: Ensure Docker is running and ports are available
5. **Firewall**: Ensure ports 8443, 1883, and 9002 are accessible
6. **Domain Resolution**: Ensure `nuri-server.local` and `nb-probook.spot.sk` resolve to the correct IP
