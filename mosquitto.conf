listener 1883
allow_anonymous true

# WebSocket Secure (WSS) listener with faster keepalive
listener 9002
protocol websockets
allow_anonymous true
#cafile /mosquitto/config/ssl/server.crt
certfile /mosquitto/config/ssl/server.crt
keyfile /mosquitto/config/ssl/server.key

# Faster disconnection detection
max_keepalive 30

# Bridge to server
connection bridge-to-server
address **************:1883
remote_username admin
remote_password a
# bridge_insecure false
# bridge_capath /etc/ssl/certs/

topic # both 0 "" intercomm/