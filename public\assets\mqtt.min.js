"use strict";var mqtt=(()=>{var ss=Object.defineProperty;var Ig=Object.getOwnPropertyDescriptor;var Tg=Object.getOwnPropertyNames;var Rg=Object.prototype.hasOwnProperty;var ge=(t,e)=>()=>(t&&(e=t(t=0)),e);var L=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),zt=(t,e)=>{for(var r in e)ss(t,r,{get:e[r],enumerable:!0})},Cg=(t,e,r,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of Tg(e))!Rg.call(t,n)&&n!==r&&ss(t,n,{get:()=>e[n],enumerable:!(i=Ig(e,n))||i.enumerable});return t};var Z=t=>Cg(ss({},"__esModule",{value:!0}),t);var _=ge(()=>{});var C={};zt(C,{_debugEnd:()=>au,_debugProcess:()=>lu,_events:()=>Au,_eventsCount:()=>Iu,_exiting:()=>Wa,_fatalExceptions:()=>iu,_getActiveHandles:()=>za,_getActiveRequests:()=>Va,_kill:()=>Qa,_linkedBinding:()=>ja,_maxListeners:()=>Su,_preload_modules:()=>mu,_rawDebug:()=>qa,_startProfilerIdleNotifier:()=>uu,_stopProfilerIdleNotifier:()=>fu,_tickCallback:()=>ou,abort:()=>pu,addListener:()=>Tu,allowedNodeEnvironmentFlags:()=>eu,arch:()=>Ia,argv:()=>Ca,argv0:()=>_u,assert:()=>tu,binding:()=>ka,chdir:()=>Ma,config:()=>$a,cpuUsage:()=>Mi,cwd:()=>Ua,debugPort:()=>wu,default:()=>Uu,dlopen:()=>Ha,domain:()=>Fa,emit:()=>xu,emitWarning:()=>Oa,env:()=>Ra,execArgv:()=>Ba,execPath:()=>bu,exit:()=>Xa,features:()=>ru,hasUncaughtExceptionCaptureCallback:()=>su,hrtime:()=>Ui,kill:()=>Ja,listeners:()=>Lu,memoryUsage:()=>Ya,moduleLoadList:()=>Da,nextTick:()=>va,off:()=>Cu,on:()=>bt,once:()=>Ru,openStdin:()=>Za,pid:()=>gu,platform:()=>Ta,ppid:()=>yu,prependListener:()=>Ou,prependOnceListener:()=>ku,reallyExit:()=>Ka,release:()=>Na,removeAllListeners:()=>Pu,removeListener:()=>Bu,resourceUsage:()=>Ga,setSourceMapsEnabled:()=>Eu,setUncaughtExceptionCaptureCallback:()=>nu,stderr:()=>hu,stdin:()=>du,stdout:()=>cu,title:()=>Aa,umask:()=>La,uptime:()=>vu,version:()=>Pa,versions:()=>xa});function as(t){throw new Error("Node.js process "+t+" is not supported by JSPM core outside of Node.js")}function Bg(){!Br||!Kt||(Br=!1,Kt.length?yt=Kt.concat(yt):Li=-1,yt.length&&Ea())}function Ea(){if(!Br){var t=setTimeout(Bg,0);Br=!0;for(var e=yt.length;e;){for(Kt=yt,yt=[];++Li<e;)Kt&&Kt[Li].run();Li=-1,e=yt.length}Kt=null,Br=!1,clearTimeout(t)}}function va(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];yt.push(new Sa(t,e)),yt.length===1&&!Br&&setTimeout(Ea,0)}function Sa(t,e){this.fun=t,this.array=e}function _e(){}function ja(t){as("_linkedBinding")}function Ha(t){as("dlopen")}function Va(){return[]}function za(){return[]}function tu(t,e){if(!t)throw new Error(e||"assertion error")}function su(){return!1}function vu(){return kt.now()/1e3}function Ui(t){var e=Math.floor((Date.now()-kt.now())*.001),r=kt.now()*.001,i=Math.floor(r)+e,n=Math.floor(r%1*1e9);return t&&(i=i-t[0],n=n-t[1],n<0&&(i--,n+=ls)),[i,n]}function bt(){return Uu}function Lu(t){return[]}var yt,Br,Kt,Li,Aa,Ia,Ta,Ra,Ca,Ba,Pa,xa,Oa,ka,La,Ua,Ma,Na,qa,Da,Fa,Wa,$a,Ka,Qa,Mi,Ga,Ya,Ja,Xa,Za,eu,ru,iu,nu,ou,lu,au,uu,fu,cu,hu,du,pu,gu,yu,bu,wu,_u,mu,Eu,kt,os,ls,Su,Au,Iu,Tu,Ru,Cu,Bu,Pu,xu,Ou,ku,Uu,Mu=ge(()=>{_();E();m();yt=[],Br=!1,Li=-1;Sa.prototype.run=function(){this.fun.apply(null,this.array)};Aa="browser",Ia="x64",Ta="browser",Ra={PATH:"/usr/bin",LANG:navigator.language+".UTF-8",PWD:"/",HOME:"/home",TMP:"/tmp"},Ca=["/usr/bin/node"],Ba=[],Pa="v16.8.0",xa={},Oa=function(t,e){console.warn((e?e+": ":"")+t)},ka=function(t){as("binding")},La=function(t){return 0},Ua=function(){return"/"},Ma=function(t){},Na={name:"node",sourceUrl:"",headersUrl:"",libUrl:""};qa=_e,Da=[];Fa={},Wa=!1,$a={};Ka=_e,Qa=_e,Mi=function(){return{}},Ga=Mi,Ya=Mi,Ja=_e,Xa=_e,Za=_e,eu={};ru={inspector:!1,debug:!1,uv:!1,ipv6:!1,tls_alpn:!1,tls_sni:!1,tls_ocsp:!1,tls:!1,cached_builtins:!0},iu=_e,nu=_e;ou=_e,lu=_e,au=_e,uu=_e,fu=_e,cu=void 0,hu=void 0,du=void 0,pu=_e,gu=2,yu=1,bu="/bin/usr/node",wu=9229,_u="node",mu=[],Eu=_e,kt={now:typeof performance<"u"?performance.now.bind(performance):void 0,timing:typeof performance<"u"?performance.timing:void 0};kt.now===void 0&&(os=Date.now(),kt.timing&&kt.timing.navigationStart&&(os=kt.timing.navigationStart),kt.now=()=>Date.now()-os);ls=1e9;Ui.bigint=function(t){var e=Ui(t);return typeof BigInt>"u"?e[0]*ls+e[1]:BigInt(e[0]*ls)+BigInt(e[1])};Su=10,Au={},Iu=0;Tu=bt,Ru=bt,Cu=bt,Bu=bt,Pu=bt,xu=_e,Ou=bt,ku=bt;Uu={version:Pa,versions:xa,arch:Ia,platform:Ta,release:Na,_rawDebug:qa,moduleLoadList:Da,binding:ka,_linkedBinding:ja,_events:Au,_eventsCount:Iu,_maxListeners:Su,on:bt,addListener:Tu,once:Ru,off:Cu,removeListener:Bu,removeAllListeners:Pu,emit:xu,prependListener:Ou,prependOnceListener:ku,listeners:Lu,domain:Fa,_exiting:Wa,config:$a,dlopen:Ha,uptime:vu,_getActiveRequests:Va,_getActiveHandles:za,reallyExit:Ka,_kill:Qa,cpuUsage:Mi,resourceUsage:Ga,memoryUsage:Ya,kill:Ja,exit:Xa,openStdin:Za,allowedNodeEnvironmentFlags:eu,assert:tu,features:ru,_fatalExceptions:iu,setUncaughtExceptionCaptureCallback:nu,hasUncaughtExceptionCaptureCallback:su,emitWarning:Oa,nextTick:va,_tickCallback:ou,_debugProcess:lu,_debugEnd:au,_startProfilerIdleNotifier:uu,_stopProfilerIdleNotifier:fu,stdout:cu,stdin:du,stderr:hu,abort:pu,umask:La,chdir:Ma,cwd:Ua,env:Ra,title:Aa,argv:Ca,execArgv:Ba,pid:gu,ppid:yu,execPath:bu,debugPort:wu,hrtime:Ui,argv0:_u,_preload_modules:mu,setSourceMapsEnabled:Eu}});var m=ge(()=>{Mu()});var me={};zt(me,{Buffer:()=>k,INSPECT_MAX_BYTES:()=>kg,default:()=>Lt,kMaxLength:()=>Lg});function Pg(){if(Nu)return oi;Nu=!0,oi.byteLength=l,oi.toByteArray=c,oi.fromByteArray=g;for(var t=[],e=[],r=typeof Uint8Array<"u"?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n=0,o=i.length;n<o;++n)t[n]=i[n],e[i.charCodeAt(n)]=n;e["-".charCodeAt(0)]=62,e["_".charCodeAt(0)]=63;function s(y){var w=y.length;if(w%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var S=y.indexOf("=");S===-1&&(S=w);var A=S===w?0:4-S%4;return[S,A]}function l(y){var w=s(y),S=w[0],A=w[1];return(S+A)*3/4-A}function u(y,w,S){return(w+S)*3/4-S}function c(y){var w,S=s(y),A=S[0],I=S[1],P=new r(u(y,A,I)),R=0,M=I>0?A-4:A,N;for(N=0;N<M;N+=4)w=e[y.charCodeAt(N)]<<18|e[y.charCodeAt(N+1)]<<12|e[y.charCodeAt(N+2)]<<6|e[y.charCodeAt(N+3)],P[R++]=w>>16&255,P[R++]=w>>8&255,P[R++]=w&255;return I===2&&(w=e[y.charCodeAt(N)]<<2|e[y.charCodeAt(N+1)]>>4,P[R++]=w&255),I===1&&(w=e[y.charCodeAt(N)]<<10|e[y.charCodeAt(N+1)]<<4|e[y.charCodeAt(N+2)]>>2,P[R++]=w>>8&255,P[R++]=w&255),P}function h(y){return t[y>>18&63]+t[y>>12&63]+t[y>>6&63]+t[y&63]}function d(y,w,S){for(var A,I=[],P=w;P<S;P+=3)A=(y[P]<<16&16711680)+(y[P+1]<<8&65280)+(y[P+2]&255),I.push(h(A));return I.join("")}function g(y){for(var w,S=y.length,A=S%3,I=[],P=16383,R=0,M=S-A;R<M;R+=P)I.push(d(y,R,R+P>M?M:R+P));return A===1?(w=y[S-1],I.push(t[w>>2]+t[w<<4&63]+"==")):A===2&&(w=(y[S-2]<<8)+y[S-1],I.push(t[w>>10]+t[w>>4&63]+t[w<<2&63]+"=")),I.join("")}return oi}function xg(){if(qu)return Ni;qu=!0;return Ni.read=function(t,e,r,i,n){var o,s,l=n*8-i-1,u=(1<<l)-1,c=u>>1,h=-7,d=r?n-1:0,g=r?-1:1,y=t[e+d];for(d+=g,o=y&(1<<-h)-1,y>>=-h,h+=l;h>0;o=o*256+t[e+d],d+=g,h-=8);for(s=o&(1<<-h)-1,o>>=-h,h+=i;h>0;s=s*256+t[e+d],d+=g,h-=8);if(o===0)o=1-c;else{if(o===u)return s?NaN:(y?-1:1)*(1/0);s=s+Math.pow(2,i),o=o-c}return(y?-1:1)*s*Math.pow(2,o-i)},Ni.write=function(t,e,r,i,n,o){var s,l,u,c=o*8-n-1,h=(1<<c)-1,d=h>>1,g=n===23?Math.pow(2,-24)-Math.pow(2,-77):0,y=i?0:o-1,w=i?1:-1,S=e<0||e===0&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(l=isNaN(e)?1:0,s=h):(s=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-s))<1&&(s--,u*=2),s+d>=1?e+=g/u:e+=g*Math.pow(2,1-d),e*u>=2&&(s++,u/=2),s+d>=h?(l=0,s=h):s+d>=1?(l=(e*u-1)*Math.pow(2,n),s=s+d):(l=e*Math.pow(2,d-1)*Math.pow(2,n),s=0));n>=8;t[r+y]=l&255,y+=w,l/=256,n-=8);for(s=s<<n|l,c+=n;c>0;t[r+y]=s&255,y+=w,s/=256,c-=8);t[r+y-w]|=S*128},Ni}function Og(){if(Du)return Qt;Du=!0;let t=Pg(),e=xg(),r=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;Qt.Buffer=s,Qt.SlowBuffer=I,Qt.INSPECT_MAX_BYTES=50;let i=2147483647;Qt.kMaxLength=i,s.TYPED_ARRAY_SUPPORT=n(),!s.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function n(){try{let p=new Uint8Array(1),a={foo:function(){return 42}};return Object.setPrototypeOf(a,Uint8Array.prototype),Object.setPrototypeOf(p,a),p.foo()===42}catch{return!1}}Object.defineProperty(s.prototype,"parent",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.buffer}}),Object.defineProperty(s.prototype,"offset",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.byteOffset}});function o(p){if(p>i)throw new RangeError('The value "'+p+'" is invalid for option "size"');let a=new Uint8Array(p);return Object.setPrototypeOf(a,s.prototype),a}function s(p,a,f){if(typeof p=="number"){if(typeof a=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return h(p)}return l(p,a,f)}s.poolSize=8192;function l(p,a,f){if(typeof p=="string")return d(p,a);if(ArrayBuffer.isView(p))return y(p);if(p==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof p);if(Ye(p,ArrayBuffer)||p&&Ye(p.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(Ye(p,SharedArrayBuffer)||p&&Ye(p.buffer,SharedArrayBuffer)))return w(p,a,f);if(typeof p=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');let b=p.valueOf&&p.valueOf();if(b!=null&&b!==p)return s.from(b,a,f);let v=S(p);if(v)return v;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof p[Symbol.toPrimitive]=="function")return s.from(p[Symbol.toPrimitive]("string"),a,f);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof p)}s.from=function(p,a,f){return l(p,a,f)},Object.setPrototypeOf(s.prototype,Uint8Array.prototype),Object.setPrototypeOf(s,Uint8Array);function u(p){if(typeof p!="number")throw new TypeError('"size" argument must be of type number');if(p<0)throw new RangeError('The value "'+p+'" is invalid for option "size"')}function c(p,a,f){return u(p),p<=0?o(p):a!==void 0?typeof f=="string"?o(p).fill(a,f):o(p).fill(a):o(p)}s.alloc=function(p,a,f){return c(p,a,f)};function h(p){return u(p),o(p<0?0:A(p)|0)}s.allocUnsafe=function(p){return h(p)},s.allocUnsafeSlow=function(p){return h(p)};function d(p,a){if((typeof a!="string"||a==="")&&(a="utf8"),!s.isEncoding(a))throw new TypeError("Unknown encoding: "+a);let f=P(p,a)|0,b=o(f),v=b.write(p,a);return v!==f&&(b=b.slice(0,v)),b}function g(p){let a=p.length<0?0:A(p.length)|0,f=o(a);for(let b=0;b<a;b+=1)f[b]=p[b]&255;return f}function y(p){if(Ye(p,Uint8Array)){let a=new Uint8Array(p);return w(a.buffer,a.byteOffset,a.byteLength)}return g(p)}function w(p,a,f){if(a<0||p.byteLength<a)throw new RangeError('"offset" is outside of buffer bounds');if(p.byteLength<a+(f||0))throw new RangeError('"length" is outside of buffer bounds');let b;return a===void 0&&f===void 0?b=new Uint8Array(p):f===void 0?b=new Uint8Array(p,a):b=new Uint8Array(p,a,f),Object.setPrototypeOf(b,s.prototype),b}function S(p){if(s.isBuffer(p)){let a=A(p.length)|0,f=o(a);return f.length===0||p.copy(f,0,0,a),f}if(p.length!==void 0)return typeof p.length!="number"||ns(p.length)?o(0):g(p);if(p.type==="Buffer"&&Array.isArray(p.data))return g(p.data)}function A(p){if(p>=i)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i.toString(16)+" bytes");return p|0}function I(p){return+p!=p&&(p=0),s.alloc(+p)}s.isBuffer=function(a){return a!=null&&a._isBuffer===!0&&a!==s.prototype},s.compare=function(a,f){if(Ye(a,Uint8Array)&&(a=s.from(a,a.offset,a.byteLength)),Ye(f,Uint8Array)&&(f=s.from(f,f.offset,f.byteLength)),!s.isBuffer(a)||!s.isBuffer(f))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(a===f)return 0;let b=a.length,v=f.length;for(let T=0,B=Math.min(b,v);T<B;++T)if(a[T]!==f[T]){b=a[T],v=f[T];break}return b<v?-1:v<b?1:0},s.isEncoding=function(a){switch(String(a).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(a,f){if(!Array.isArray(a))throw new TypeError('"list" argument must be an Array of Buffers');if(a.length===0)return s.alloc(0);let b;if(f===void 0)for(f=0,b=0;b<a.length;++b)f+=a[b].length;let v=s.allocUnsafe(f),T=0;for(b=0;b<a.length;++b){let B=a[b];if(Ye(B,Uint8Array))T+B.length>v.length?(s.isBuffer(B)||(B=s.from(B)),B.copy(v,T)):Uint8Array.prototype.set.call(v,B,T);else if(s.isBuffer(B))B.copy(v,T);else throw new TypeError('"list" argument must be an Array of Buffers');T+=B.length}return v};function P(p,a){if(s.isBuffer(p))return p.length;if(ArrayBuffer.isView(p)||Ye(p,ArrayBuffer))return p.byteLength;if(typeof p!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof p);let f=p.length,b=arguments.length>2&&arguments[2]===!0;if(!b&&f===0)return 0;let v=!1;for(;;)switch(a){case"ascii":case"latin1":case"binary":return f;case"utf8":case"utf-8":return is(p).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return f*2;case"hex":return f>>>1;case"base64":return ma(p).length;default:if(v)return b?-1:is(p).length;a=(""+a).toLowerCase(),v=!0}}s.byteLength=P;function R(p,a,f){let b=!1;if((a===void 0||a<0)&&(a=0),a>this.length||((f===void 0||f>this.length)&&(f=this.length),f<=0)||(f>>>=0,a>>>=0,f<=a))return"";for(p||(p="utf8");;)switch(p){case"hex":return yg(this,a,f);case"utf8":case"utf-8":return Ar(this,a,f);case"ascii":return ts(this,a,f);case"latin1":case"binary":return gg(this,a,f);case"base64":return we(this,a,f);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return bg(this,a,f);default:if(b)throw new TypeError("Unknown encoding: "+p);p=(p+"").toLowerCase(),b=!0}}s.prototype._isBuffer=!0;function M(p,a,f){let b=p[a];p[a]=p[f],p[f]=b}s.prototype.swap16=function(){let a=this.length;if(a%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let f=0;f<a;f+=2)M(this,f,f+1);return this},s.prototype.swap32=function(){let a=this.length;if(a%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let f=0;f<a;f+=4)M(this,f,f+3),M(this,f+1,f+2);return this},s.prototype.swap64=function(){let a=this.length;if(a%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let f=0;f<a;f+=8)M(this,f,f+7),M(this,f+1,f+6),M(this,f+2,f+5),M(this,f+3,f+4);return this},s.prototype.toString=function(){let a=this.length;return a===0?"":arguments.length===0?Ar(this,0,a):R.apply(this,arguments)},s.prototype.toLocaleString=s.prototype.toString,s.prototype.equals=function(a){if(!s.isBuffer(a))throw new TypeError("Argument must be a Buffer");return this===a?!0:s.compare(this,a)===0},s.prototype.inspect=function(){let a="",f=Qt.INSPECT_MAX_BYTES;return a=this.toString("hex",0,f).replace(/(.{2})/g,"$1 ").trim(),this.length>f&&(a+=" ... "),"<Buffer "+a+">"},r&&(s.prototype[r]=s.prototype.inspect),s.prototype.compare=function(a,f,b,v,T){if(Ye(a,Uint8Array)&&(a=s.from(a,a.offset,a.byteLength)),!s.isBuffer(a))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof a);if(f===void 0&&(f=0),b===void 0&&(b=a?a.length:0),v===void 0&&(v=0),T===void 0&&(T=this.length),f<0||b>a.length||v<0||T>this.length)throw new RangeError("out of range index");if(v>=T&&f>=b)return 0;if(v>=T)return-1;if(f>=b)return 1;if(f>>>=0,b>>>=0,v>>>=0,T>>>=0,this===a)return 0;let B=T-v,W=b-f,se=Math.min(B,W),te=this.slice(v,T),oe=a.slice(f,b);for(let J=0;J<se;++J)if(te[J]!==oe[J]){B=te[J],W=oe[J];break}return B<W?-1:W<B?1:0};function N(p,a,f,b,v){if(p.length===0)return-1;if(typeof f=="string"?(b=f,f=0):f>2147483647?f=2147483647:f<-2147483648&&(f=-2147483648),f=+f,ns(f)&&(f=v?0:p.length-1),f<0&&(f=p.length+f),f>=p.length){if(v)return-1;f=p.length-1}else if(f<0)if(v)f=0;else return-1;if(typeof a=="string"&&(a=s.from(a,b)),s.isBuffer(a))return a.length===0?-1:V(p,a,f,b,v);if(typeof a=="number")return a=a&255,typeof Uint8Array.prototype.indexOf=="function"?v?Uint8Array.prototype.indexOf.call(p,a,f):Uint8Array.prototype.lastIndexOf.call(p,a,f):V(p,[a],f,b,v);throw new TypeError("val must be string, number or Buffer")}function V(p,a,f,b,v){let T=1,B=p.length,W=a.length;if(b!==void 0&&(b=String(b).toLowerCase(),b==="ucs2"||b==="ucs-2"||b==="utf16le"||b==="utf-16le")){if(p.length<2||a.length<2)return-1;T=2,B/=2,W/=2,f/=2}function se(oe,J){return T===1?oe[J]:oe.readUInt16BE(J*T)}let te;if(v){let oe=-1;for(te=f;te<B;te++)if(se(p,te)===se(a,oe===-1?0:te-oe)){if(oe===-1&&(oe=te),te-oe+1===W)return oe*T}else oe!==-1&&(te-=te-oe),oe=-1}else for(f+W>B&&(f=B-W),te=f;te>=0;te--){let oe=!0;for(let J=0;J<W;J++)if(se(p,te+J)!==se(a,J)){oe=!1;break}if(oe)return te}return-1}s.prototype.includes=function(a,f,b){return this.indexOf(a,f,b)!==-1},s.prototype.indexOf=function(a,f,b){return N(this,a,f,b,!0)},s.prototype.lastIndexOf=function(a,f,b){return N(this,a,f,b,!1)};function Q(p,a,f,b){f=Number(f)||0;let v=p.length-f;b?(b=Number(b),b>v&&(b=v)):b=v;let T=a.length;b>T/2&&(b=T/2);let B;for(B=0;B<b;++B){let W=parseInt(a.substr(B*2,2),16);if(ns(W))return B;p[f+B]=W}return B}function z(p,a,f,b){return ki(is(a,p.length-f),p,f,b)}function Y(p,a,f,b){return ki(Eg(a),p,f,b)}function ve(p,a,f,b){return ki(ma(a),p,f,b)}function ni(p,a,f,b){return ki(vg(a,p.length-f),p,f,b)}s.prototype.write=function(a,f,b,v){if(f===void 0)v="utf8",b=this.length,f=0;else if(b===void 0&&typeof f=="string")v=f,b=this.length,f=0;else if(isFinite(f))f=f>>>0,isFinite(b)?(b=b>>>0,v===void 0&&(v="utf8")):(v=b,b=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let T=this.length-f;if((b===void 0||b>T)&&(b=T),a.length>0&&(b<0||f<0)||f>this.length)throw new RangeError("Attempt to write outside buffer bounds");v||(v="utf8");let B=!1;for(;;)switch(v){case"hex":return Q(this,a,f,b);case"utf8":case"utf-8":return z(this,a,f,b);case"ascii":case"latin1":case"binary":return Y(this,a,f,b);case"base64":return ve(this,a,f,b);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return ni(this,a,f,b);default:if(B)throw new TypeError("Unknown encoding: "+v);v=(""+v).toLowerCase(),B=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function we(p,a,f){return a===0&&f===p.length?t.fromByteArray(p):t.fromByteArray(p.slice(a,f))}function Ar(p,a,f){f=Math.min(p.length,f);let b=[],v=a;for(;v<f;){let T=p[v],B=null,W=T>239?4:T>223?3:T>191?2:1;if(v+W<=f){let se,te,oe,J;switch(W){case 1:T<128&&(B=T);break;case 2:se=p[v+1],(se&192)===128&&(J=(T&31)<<6|se&63,J>127&&(B=J));break;case 3:se=p[v+1],te=p[v+2],(se&192)===128&&(te&192)===128&&(J=(T&15)<<12|(se&63)<<6|te&63,J>2047&&(J<55296||J>57343)&&(B=J));break;case 4:se=p[v+1],te=p[v+2],oe=p[v+3],(se&192)===128&&(te&192)===128&&(oe&192)===128&&(J=(T&15)<<18|(se&63)<<12|(te&63)<<6|oe&63,J>65535&&J<1114112&&(B=J))}}B===null?(B=65533,W=1):B>65535&&(B-=65536,b.push(B>>>10&1023|55296),B=56320|B&1023),b.push(B),v+=W}return Tr(b)}let Ir=4096;function Tr(p){let a=p.length;if(a<=Ir)return String.fromCharCode.apply(String,p);let f="",b=0;for(;b<a;)f+=String.fromCharCode.apply(String,p.slice(b,b+=Ir));return f}function ts(p,a,f){let b="";f=Math.min(p.length,f);for(let v=a;v<f;++v)b+=String.fromCharCode(p[v]&127);return b}function gg(p,a,f){let b="";f=Math.min(p.length,f);for(let v=a;v<f;++v)b+=String.fromCharCode(p[v]);return b}function yg(p,a,f){let b=p.length;(!a||a<0)&&(a=0),(!f||f<0||f>b)&&(f=b);let v="";for(let T=a;T<f;++T)v+=Sg[p[T]];return v}function bg(p,a,f){let b=p.slice(a,f),v="";for(let T=0;T<b.length-1;T+=2)v+=String.fromCharCode(b[T]+b[T+1]*256);return v}s.prototype.slice=function(a,f){let b=this.length;a=~~a,f=f===void 0?b:~~f,a<0?(a+=b,a<0&&(a=0)):a>b&&(a=b),f<0?(f+=b,f<0&&(f=0)):f>b&&(f=b),f<a&&(f=a);let v=this.subarray(a,f);return Object.setPrototypeOf(v,s.prototype),v};function de(p,a,f){if(p%1!==0||p<0)throw new RangeError("offset is not uint");if(p+a>f)throw new RangeError("Trying to access beyond buffer length")}s.prototype.readUintLE=s.prototype.readUIntLE=function(a,f,b){a=a>>>0,f=f>>>0,b||de(a,f,this.length);let v=this[a],T=1,B=0;for(;++B<f&&(T*=256);)v+=this[a+B]*T;return v},s.prototype.readUintBE=s.prototype.readUIntBE=function(a,f,b){a=a>>>0,f=f>>>0,b||de(a,f,this.length);let v=this[a+--f],T=1;for(;f>0&&(T*=256);)v+=this[a+--f]*T;return v},s.prototype.readUint8=s.prototype.readUInt8=function(a,f){return a=a>>>0,f||de(a,1,this.length),this[a]},s.prototype.readUint16LE=s.prototype.readUInt16LE=function(a,f){return a=a>>>0,f||de(a,2,this.length),this[a]|this[a+1]<<8},s.prototype.readUint16BE=s.prototype.readUInt16BE=function(a,f){return a=a>>>0,f||de(a,2,this.length),this[a]<<8|this[a+1]},s.prototype.readUint32LE=s.prototype.readUInt32LE=function(a,f){return a=a>>>0,f||de(a,4,this.length),(this[a]|this[a+1]<<8|this[a+2]<<16)+this[a+3]*16777216},s.prototype.readUint32BE=s.prototype.readUInt32BE=function(a,f){return a=a>>>0,f||de(a,4,this.length),this[a]*16777216+(this[a+1]<<16|this[a+2]<<8|this[a+3])},s.prototype.readBigUInt64LE=Ot(function(a){a=a>>>0,Cr(a,"offset");let f=this[a],b=this[a+7];(f===void 0||b===void 0)&&si(a,this.length-8);let v=f+this[++a]*2**8+this[++a]*2**16+this[++a]*2**24,T=this[++a]+this[++a]*2**8+this[++a]*2**16+b*2**24;return BigInt(v)+(BigInt(T)<<BigInt(32))}),s.prototype.readBigUInt64BE=Ot(function(a){a=a>>>0,Cr(a,"offset");let f=this[a],b=this[a+7];(f===void 0||b===void 0)&&si(a,this.length-8);let v=f*2**24+this[++a]*2**16+this[++a]*2**8+this[++a],T=this[++a]*2**24+this[++a]*2**16+this[++a]*2**8+b;return(BigInt(v)<<BigInt(32))+BigInt(T)}),s.prototype.readIntLE=function(a,f,b){a=a>>>0,f=f>>>0,b||de(a,f,this.length);let v=this[a],T=1,B=0;for(;++B<f&&(T*=256);)v+=this[a+B]*T;return T*=128,v>=T&&(v-=Math.pow(2,8*f)),v},s.prototype.readIntBE=function(a,f,b){a=a>>>0,f=f>>>0,b||de(a,f,this.length);let v=f,T=1,B=this[a+--v];for(;v>0&&(T*=256);)B+=this[a+--v]*T;return T*=128,B>=T&&(B-=Math.pow(2,8*f)),B},s.prototype.readInt8=function(a,f){return a=a>>>0,f||de(a,1,this.length),this[a]&128?(255-this[a]+1)*-1:this[a]},s.prototype.readInt16LE=function(a,f){a=a>>>0,f||de(a,2,this.length);let b=this[a]|this[a+1]<<8;return b&32768?b|4294901760:b},s.prototype.readInt16BE=function(a,f){a=a>>>0,f||de(a,2,this.length);let b=this[a+1]|this[a]<<8;return b&32768?b|4294901760:b},s.prototype.readInt32LE=function(a,f){return a=a>>>0,f||de(a,4,this.length),this[a]|this[a+1]<<8|this[a+2]<<16|this[a+3]<<24},s.prototype.readInt32BE=function(a,f){return a=a>>>0,f||de(a,4,this.length),this[a]<<24|this[a+1]<<16|this[a+2]<<8|this[a+3]},s.prototype.readBigInt64LE=Ot(function(a){a=a>>>0,Cr(a,"offset");let f=this[a],b=this[a+7];(f===void 0||b===void 0)&&si(a,this.length-8);let v=this[a+4]+this[a+5]*2**8+this[a+6]*2**16+(b<<24);return(BigInt(v)<<BigInt(32))+BigInt(f+this[++a]*2**8+this[++a]*2**16+this[++a]*2**24)}),s.prototype.readBigInt64BE=Ot(function(a){a=a>>>0,Cr(a,"offset");let f=this[a],b=this[a+7];(f===void 0||b===void 0)&&si(a,this.length-8);let v=(f<<24)+this[++a]*2**16+this[++a]*2**8+this[++a];return(BigInt(v)<<BigInt(32))+BigInt(this[++a]*2**24+this[++a]*2**16+this[++a]*2**8+b)}),s.prototype.readFloatLE=function(a,f){return a=a>>>0,f||de(a,4,this.length),e.read(this,a,!0,23,4)},s.prototype.readFloatBE=function(a,f){return a=a>>>0,f||de(a,4,this.length),e.read(this,a,!1,23,4)},s.prototype.readDoubleLE=function(a,f){return a=a>>>0,f||de(a,8,this.length),e.read(this,a,!0,52,8)},s.prototype.readDoubleBE=function(a,f){return a=a>>>0,f||de(a,8,this.length),e.read(this,a,!1,52,8)};function Ce(p,a,f,b,v,T){if(!s.isBuffer(p))throw new TypeError('"buffer" argument must be a Buffer instance');if(a>v||a<T)throw new RangeError('"value" argument is out of bounds');if(f+b>p.length)throw new RangeError("Index out of range")}s.prototype.writeUintLE=s.prototype.writeUIntLE=function(a,f,b,v){if(a=+a,f=f>>>0,b=b>>>0,!v){let W=Math.pow(2,8*b)-1;Ce(this,a,f,b,W,0)}let T=1,B=0;for(this[f]=a&255;++B<b&&(T*=256);)this[f+B]=a/T&255;return f+b},s.prototype.writeUintBE=s.prototype.writeUIntBE=function(a,f,b,v){if(a=+a,f=f>>>0,b=b>>>0,!v){let W=Math.pow(2,8*b)-1;Ce(this,a,f,b,W,0)}let T=b-1,B=1;for(this[f+T]=a&255;--T>=0&&(B*=256);)this[f+T]=a/B&255;return f+b},s.prototype.writeUint8=s.prototype.writeUInt8=function(a,f,b){return a=+a,f=f>>>0,b||Ce(this,a,f,1,255,0),this[f]=a&255,f+1},s.prototype.writeUint16LE=s.prototype.writeUInt16LE=function(a,f,b){return a=+a,f=f>>>0,b||Ce(this,a,f,2,65535,0),this[f]=a&255,this[f+1]=a>>>8,f+2},s.prototype.writeUint16BE=s.prototype.writeUInt16BE=function(a,f,b){return a=+a,f=f>>>0,b||Ce(this,a,f,2,65535,0),this[f]=a>>>8,this[f+1]=a&255,f+2},s.prototype.writeUint32LE=s.prototype.writeUInt32LE=function(a,f,b){return a=+a,f=f>>>0,b||Ce(this,a,f,4,4294967295,0),this[f+3]=a>>>24,this[f+2]=a>>>16,this[f+1]=a>>>8,this[f]=a&255,f+4},s.prototype.writeUint32BE=s.prototype.writeUInt32BE=function(a,f,b){return a=+a,f=f>>>0,b||Ce(this,a,f,4,4294967295,0),this[f]=a>>>24,this[f+1]=a>>>16,this[f+2]=a>>>8,this[f+3]=a&255,f+4};function da(p,a,f,b,v){_a(a,b,v,p,f,7);let T=Number(a&BigInt(4294967295));p[f++]=T,T=T>>8,p[f++]=T,T=T>>8,p[f++]=T,T=T>>8,p[f++]=T;let B=Number(a>>BigInt(32)&BigInt(4294967295));return p[f++]=B,B=B>>8,p[f++]=B,B=B>>8,p[f++]=B,B=B>>8,p[f++]=B,f}function pa(p,a,f,b,v){_a(a,b,v,p,f,7);let T=Number(a&BigInt(4294967295));p[f+7]=T,T=T>>8,p[f+6]=T,T=T>>8,p[f+5]=T,T=T>>8,p[f+4]=T;let B=Number(a>>BigInt(32)&BigInt(4294967295));return p[f+3]=B,B=B>>8,p[f+2]=B,B=B>>8,p[f+1]=B,B=B>>8,p[f]=B,f+8}s.prototype.writeBigUInt64LE=Ot(function(a,f=0){return da(this,a,f,BigInt(0),BigInt("0xffffffffffffffff"))}),s.prototype.writeBigUInt64BE=Ot(function(a,f=0){return pa(this,a,f,BigInt(0),BigInt("0xffffffffffffffff"))}),s.prototype.writeIntLE=function(a,f,b,v){if(a=+a,f=f>>>0,!v){let se=Math.pow(2,8*b-1);Ce(this,a,f,b,se-1,-se)}let T=0,B=1,W=0;for(this[f]=a&255;++T<b&&(B*=256);)a<0&&W===0&&this[f+T-1]!==0&&(W=1),this[f+T]=(a/B>>0)-W&255;return f+b},s.prototype.writeIntBE=function(a,f,b,v){if(a=+a,f=f>>>0,!v){let se=Math.pow(2,8*b-1);Ce(this,a,f,b,se-1,-se)}let T=b-1,B=1,W=0;for(this[f+T]=a&255;--T>=0&&(B*=256);)a<0&&W===0&&this[f+T+1]!==0&&(W=1),this[f+T]=(a/B>>0)-W&255;return f+b},s.prototype.writeInt8=function(a,f,b){return a=+a,f=f>>>0,b||Ce(this,a,f,1,127,-128),a<0&&(a=255+a+1),this[f]=a&255,f+1},s.prototype.writeInt16LE=function(a,f,b){return a=+a,f=f>>>0,b||Ce(this,a,f,2,32767,-32768),this[f]=a&255,this[f+1]=a>>>8,f+2},s.prototype.writeInt16BE=function(a,f,b){return a=+a,f=f>>>0,b||Ce(this,a,f,2,32767,-32768),this[f]=a>>>8,this[f+1]=a&255,f+2},s.prototype.writeInt32LE=function(a,f,b){return a=+a,f=f>>>0,b||Ce(this,a,f,4,2147483647,-2147483648),this[f]=a&255,this[f+1]=a>>>8,this[f+2]=a>>>16,this[f+3]=a>>>24,f+4},s.prototype.writeInt32BE=function(a,f,b){return a=+a,f=f>>>0,b||Ce(this,a,f,4,2147483647,-2147483648),a<0&&(a=4294967295+a+1),this[f]=a>>>24,this[f+1]=a>>>16,this[f+2]=a>>>8,this[f+3]=a&255,f+4},s.prototype.writeBigInt64LE=Ot(function(a,f=0){return da(this,a,f,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),s.prototype.writeBigInt64BE=Ot(function(a,f=0){return pa(this,a,f,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});function ga(p,a,f,b,v,T){if(f+b>p.length)throw new RangeError("Index out of range");if(f<0)throw new RangeError("Index out of range")}function ya(p,a,f,b,v){return a=+a,f=f>>>0,v||ga(p,a,f,4),e.write(p,a,f,b,23,4),f+4}s.prototype.writeFloatLE=function(a,f,b){return ya(this,a,f,!0,b)},s.prototype.writeFloatBE=function(a,f,b){return ya(this,a,f,!1,b)};function ba(p,a,f,b,v){return a=+a,f=f>>>0,v||ga(p,a,f,8),e.write(p,a,f,b,52,8),f+8}s.prototype.writeDoubleLE=function(a,f,b){return ba(this,a,f,!0,b)},s.prototype.writeDoubleBE=function(a,f,b){return ba(this,a,f,!1,b)},s.prototype.copy=function(a,f,b,v){if(!s.isBuffer(a))throw new TypeError("argument should be a Buffer");if(b||(b=0),!v&&v!==0&&(v=this.length),f>=a.length&&(f=a.length),f||(f=0),v>0&&v<b&&(v=b),v===b||a.length===0||this.length===0)return 0;if(f<0)throw new RangeError("targetStart out of bounds");if(b<0||b>=this.length)throw new RangeError("Index out of range");if(v<0)throw new RangeError("sourceEnd out of bounds");v>this.length&&(v=this.length),a.length-f<v-b&&(v=a.length-f+b);let T=v-b;return this===a&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(f,b,v):Uint8Array.prototype.set.call(a,this.subarray(b,v),f),T},s.prototype.fill=function(a,f,b,v){if(typeof a=="string"){if(typeof f=="string"?(v=f,f=0,b=this.length):typeof b=="string"&&(v=b,b=this.length),v!==void 0&&typeof v!="string")throw new TypeError("encoding must be a string");if(typeof v=="string"&&!s.isEncoding(v))throw new TypeError("Unknown encoding: "+v);if(a.length===1){let B=a.charCodeAt(0);(v==="utf8"&&B<128||v==="latin1")&&(a=B)}}else typeof a=="number"?a=a&255:typeof a=="boolean"&&(a=Number(a));if(f<0||this.length<f||this.length<b)throw new RangeError("Out of range index");if(b<=f)return this;f=f>>>0,b=b===void 0?this.length:b>>>0,a||(a=0);let T;if(typeof a=="number")for(T=f;T<b;++T)this[T]=a;else{let B=s.isBuffer(a)?a:s.from(a,v),W=B.length;if(W===0)throw new TypeError('The value "'+a+'" is invalid for argument "value"');for(T=0;T<b-f;++T)this[T+f]=B[T%W]}return this};let Rr={};function rs(p,a,f){Rr[p]=class extends f{constructor(){super(),Object.defineProperty(this,"message",{value:a.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${p}]`,this.stack,delete this.name}get code(){return p}set code(v){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:v,writable:!0})}toString(){return`${this.name} [${p}]: ${this.message}`}}}rs("ERR_BUFFER_OUT_OF_BOUNDS",function(p){return p?`${p} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),rs("ERR_INVALID_ARG_TYPE",function(p,a){return`The "${p}" argument must be of type number. Received type ${typeof a}`},TypeError),rs("ERR_OUT_OF_RANGE",function(p,a,f){let b=`The value of "${p}" is out of range.`,v=f;return Number.isInteger(f)&&Math.abs(f)>2**32?v=wa(String(f)):typeof f=="bigint"&&(v=String(f),(f>BigInt(2)**BigInt(32)||f<-(BigInt(2)**BigInt(32)))&&(v=wa(v)),v+="n"),b+=` It must be ${a}. Received ${v}`,b},RangeError);function wa(p){let a="",f=p.length,b=p[0]==="-"?1:0;for(;f>=b+4;f-=3)a=`_${p.slice(f-3,f)}${a}`;return`${p.slice(0,f)}${a}`}function wg(p,a,f){Cr(a,"offset"),(p[a]===void 0||p[a+f]===void 0)&&si(a,p.length-(f+1))}function _a(p,a,f,b,v,T){if(p>f||p<a){let B=typeof a=="bigint"?"n":"",W;throw T>3?a===0||a===BigInt(0)?W=`>= 0${B} and < 2${B} ** ${(T+1)*8}${B}`:W=`>= -(2${B} ** ${(T+1)*8-1}${B}) and < 2 ** ${(T+1)*8-1}${B}`:W=`>= ${a}${B} and <= ${f}${B}`,new Rr.ERR_OUT_OF_RANGE("value",W,p)}wg(b,v,T)}function Cr(p,a){if(typeof p!="number")throw new Rr.ERR_INVALID_ARG_TYPE(a,"number",p)}function si(p,a,f){throw Math.floor(p)!==p?(Cr(p,f),new Rr.ERR_OUT_OF_RANGE(f||"offset","an integer",p)):a<0?new Rr.ERR_BUFFER_OUT_OF_BOUNDS:new Rr.ERR_OUT_OF_RANGE(f||"offset",`>= ${f?1:0} and <= ${a}`,p)}let _g=/[^+/0-9A-Za-z-_]/g;function mg(p){if(p=p.split("=")[0],p=p.trim().replace(_g,""),p.length<2)return"";for(;p.length%4!==0;)p=p+"=";return p}function is(p,a){a=a||1/0;let f,b=p.length,v=null,T=[];for(let B=0;B<b;++B){if(f=p.charCodeAt(B),f>55295&&f<57344){if(!v){if(f>56319){(a-=3)>-1&&T.push(239,191,189);continue}else if(B+1===b){(a-=3)>-1&&T.push(239,191,189);continue}v=f;continue}if(f<56320){(a-=3)>-1&&T.push(239,191,189),v=f;continue}f=(v-55296<<10|f-56320)+65536}else v&&(a-=3)>-1&&T.push(239,191,189);if(v=null,f<128){if((a-=1)<0)break;T.push(f)}else if(f<2048){if((a-=2)<0)break;T.push(f>>6|192,f&63|128)}else if(f<65536){if((a-=3)<0)break;T.push(f>>12|224,f>>6&63|128,f&63|128)}else if(f<1114112){if((a-=4)<0)break;T.push(f>>18|240,f>>12&63|128,f>>6&63|128,f&63|128)}else throw new Error("Invalid code point")}return T}function Eg(p){let a=[];for(let f=0;f<p.length;++f)a.push(p.charCodeAt(f)&255);return a}function vg(p,a){let f,b,v,T=[];for(let B=0;B<p.length&&!((a-=2)<0);++B)f=p.charCodeAt(B),b=f>>8,v=f%256,T.push(v),T.push(b);return T}function ma(p){return t.toByteArray(mg(p))}function ki(p,a,f,b){let v;for(v=0;v<b&&!(v+f>=a.length||v>=p.length);++v)a[v+f]=p[v];return v}function Ye(p,a){return p instanceof a||p!=null&&p.constructor!=null&&p.constructor.name!=null&&p.constructor.name===a.name}function ns(p){return p!==p}let Sg=function(){let p="0123456789abcdef",a=new Array(256);for(let f=0;f<16;++f){let b=f*16;for(let v=0;v<16;++v)a[b+v]=p[f]+p[v]}return a}();function Ot(p){return typeof BigInt>"u"?Ag:p}function Ag(){throw new Error("BigInt not supported")}return Qt}var oi,Nu,Ni,qu,Qt,Du,Lt,k,kg,Lg,ye=ge(()=>{_();E();m();oi={},Nu=!1;Ni={},qu=!1;Qt={},Du=!1;Lt=Og();Lt.Buffer;Lt.SlowBuffer;Lt.INSPECT_MAX_BYTES;Lt.kMaxLength;k=Lt.Buffer,kg=Lt.INSPECT_MAX_BYTES,Lg=Lt.kMaxLength});var E=ge(()=>{ye()});var ju=L(fs=>{"use strict";_();E();m();Object.defineProperty(fs,"__esModule",{value:!0});var us=class{constructor(e){this.aliasToTopic={},this.max=e}put(e,r){return r===0||r>this.max?!1:(this.aliasToTopic[r]=e,this.length=Object.keys(this.aliasToTopic).length,!0)}getTopicByAlias(e){return this.aliasToTopic[e]}clear(){this.aliasToTopic={}}};fs.default=us});var ce=L((YS,Fu)=>{"use strict";_();E();m();Fu.exports={ArrayIsArray(t){return Array.isArray(t)},ArrayPrototypeIncludes(t,e){return t.includes(e)},ArrayPrototypeIndexOf(t,e){return t.indexOf(e)},ArrayPrototypeJoin(t,e){return t.join(e)},ArrayPrototypeMap(t,e){return t.map(e)},ArrayPrototypePop(t,e){return t.pop(e)},ArrayPrototypePush(t,e){return t.push(e)},ArrayPrototypeSlice(t,e,r){return t.slice(e,r)},Error,FunctionPrototypeCall(t,e,...r){return t.call(e,...r)},FunctionPrototypeSymbolHasInstance(t,e){return Function.prototype[Symbol.hasInstance].call(t,e)},MathFloor:Math.floor,Number,NumberIsInteger:Number.isInteger,NumberIsNaN:Number.isNaN,NumberMAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER,NumberMIN_SAFE_INTEGER:Number.MIN_SAFE_INTEGER,NumberParseInt:Number.parseInt,ObjectDefineProperties(t,e){return Object.defineProperties(t,e)},ObjectDefineProperty(t,e,r){return Object.defineProperty(t,e,r)},ObjectGetOwnPropertyDescriptor(t,e){return Object.getOwnPropertyDescriptor(t,e)},ObjectKeys(t){return Object.keys(t)},ObjectSetPrototypeOf(t,e){return Object.setPrototypeOf(t,e)},Promise,PromisePrototypeCatch(t,e){return t.catch(e)},PromisePrototypeThen(t,e,r){return t.then(e,r)},PromiseReject(t){return Promise.reject(t)},ReflectApply:Reflect.apply,RegExpPrototypeTest(t,e){return t.test(e)},SafeSet:Set,String,StringPrototypeSlice(t,e,r){return t.slice(e,r)},StringPrototypeToLowerCase(t){return t.toLowerCase()},StringPrototypeToUpperCase(t){return t.toUpperCase()},StringPrototypeTrim(t){return t.trim()},Symbol,SymbolFor:Symbol.for,SymbolAsyncIterator:Symbol.asyncIterator,SymbolHasInstance:Symbol.hasInstance,SymbolIterator:Symbol.iterator,TypedArrayPrototypeSet(t,e,r){return t.set(e,r)},Uint8Array}});var Je=L((iA,hs)=>{"use strict";_();E();m();var Ug=(ye(),Z(me)),Mg=Object.getPrototypeOf(async function(){}).constructor,Wu=globalThis.Blob||Ug.Blob,Ng=typeof Wu<"u"?function(e){return e instanceof Wu}:function(e){return!1},cs=class extends Error{constructor(e){if(!Array.isArray(e))throw new TypeError(`Expected input to be an Array, got ${typeof e}`);let r="";for(let i=0;i<e.length;i++)r+=`    ${e[i].stack}
`;super(r),this.name="AggregateError",this.errors=e}};hs.exports={AggregateError:cs,kEmptyObject:Object.freeze({}),once(t){let e=!1;return function(...r){e||(e=!0,t.apply(this,r))}},createDeferredPromise:function(){let t,e;return{promise:new Promise((i,n)=>{t=i,e=n}),resolve:t,reject:e}},promisify(t){return new Promise((e,r)=>{t((i,...n)=>i?r(i):e(...n))})},debuglog(){return function(){}},format(t,...e){return t.replace(/%([sdifj])/g,function(...[r,i]){let n=e.shift();return i==="f"?n.toFixed(6):i==="j"?JSON.stringify(n):i==="s"&&typeof n=="object"?`${n.constructor!==Object?n.constructor.name:""} {}`.trim():n.toString()})},inspect(t){switch(typeof t){case"string":if(t.includes("'"))if(t.includes('"')){if(!t.includes("`")&&!t.includes("${"))return`\`${t}\``}else return`"${t}"`;return`'${t}'`;case"number":return isNaN(t)?"NaN":Object.is(t,-0)?String(t):t;case"bigint":return`${String(t)}n`;case"boolean":case"undefined":return String(t);case"object":return"{}"}},types:{isAsyncFunction(t){return t instanceof Mg},isArrayBufferView(t){return ArrayBuffer.isView(t)}},isBlob:Ng};hs.exports.promisify.custom=Symbol.for("nodejs.util.promisify.custom")});var Di=L((fA,qi)=>{"use strict";_();E();m();var{AbortController:$u,AbortSignal:qg}=typeof self<"u"?self:typeof window<"u"?window:void 0;qi.exports=$u;qi.exports.AbortSignal=qg;qi.exports.default=$u});var Se=L((bA,zu)=>{"use strict";_();E();m();var{format:Dg,inspect:ji,AggregateError:jg}=Je(),Fg=globalThis.AggregateError||jg,Wg=Symbol("kIsNodeError"),$g=["string","function","number","object","Function","Object","boolean","bigint","symbol"],Hg=/^([A-Z][a-z0-9]*)+$/,Vg="__node_internal_",Fi={};function Gt(t,e){if(!t)throw new Fi.ERR_INTERNAL_ASSERTION(e)}function Hu(t){let e="",r=t.length,i=t[0]==="-"?1:0;for(;r>=i+4;r-=3)e=`_${t.slice(r-3,r)}${e}`;return`${t.slice(0,r)}${e}`}function zg(t,e,r){if(typeof e=="function")return Gt(e.length<=r.length,`Code: ${t}; The provided arguments length (${r.length}) does not match the required ones (${e.length}).`),e(...r);let i=(e.match(/%[dfijoOs]/g)||[]).length;return Gt(i===r.length,`Code: ${t}; The provided arguments length (${r.length}) does not match the required ones (${i}).`),r.length===0?e:Dg(e,...r)}function be(t,e,r){r||(r=Error);class i extends r{constructor(...o){super(zg(t,e,o))}toString(){return`${this.name} [${t}]: ${this.message}`}}Object.defineProperties(i.prototype,{name:{value:r.name,writable:!0,enumerable:!1,configurable:!0},toString:{value(){return`${this.name} [${t}]: ${this.message}`},writable:!0,enumerable:!1,configurable:!0}}),i.prototype.code=t,i.prototype[Wg]=!0,Fi[t]=i}function Vu(t){let e=Vg+t.name;return Object.defineProperty(t,"name",{value:e}),t}function Kg(t,e){if(t&&e&&t!==e){if(Array.isArray(e.errors))return e.errors.push(t),e;let r=new Fg([e,t],e.message);return r.code=e.code,r}return t||e}var ds=class extends Error{constructor(e="The operation was aborted",r=void 0){if(r!==void 0&&typeof r!="object")throw new Fi.ERR_INVALID_ARG_TYPE("options","Object",r);super(e,r),this.code="ABORT_ERR",this.name="AbortError"}};be("ERR_ASSERTION","%s",Error);be("ERR_INVALID_ARG_TYPE",(t,e,r)=>{Gt(typeof t=="string","'name' must be a string"),Array.isArray(e)||(e=[e]);let i="The ";t.endsWith(" argument")?i+=`${t} `:i+=`"${t}" ${t.includes(".")?"property":"argument"} `,i+="must be ";let n=[],o=[],s=[];for(let u of e)Gt(typeof u=="string","All expected entries have to be of type string"),$g.includes(u)?n.push(u.toLowerCase()):Hg.test(u)?o.push(u):(Gt(u!=="object",'The value "object" should be written as "Object"'),s.push(u));if(o.length>0){let u=n.indexOf("object");u!==-1&&(n.splice(n,u,1),o.push("Object"))}if(n.length>0){switch(n.length){case 1:i+=`of type ${n[0]}`;break;case 2:i+=`one of type ${n[0]} or ${n[1]}`;break;default:{let u=n.pop();i+=`one of type ${n.join(", ")}, or ${u}`}}(o.length>0||s.length>0)&&(i+=" or ")}if(o.length>0){switch(o.length){case 1:i+=`an instance of ${o[0]}`;break;case 2:i+=`an instance of ${o[0]} or ${o[1]}`;break;default:{let u=o.pop();i+=`an instance of ${o.join(", ")}, or ${u}`}}s.length>0&&(i+=" or ")}switch(s.length){case 0:break;case 1:s[0].toLowerCase()!==s[0]&&(i+="an "),i+=`${s[0]}`;break;case 2:i+=`one of ${s[0]} or ${s[1]}`;break;default:{let u=s.pop();i+=`one of ${s.join(", ")}, or ${u}`}}if(r==null)i+=`. Received ${r}`;else if(typeof r=="function"&&r.name)i+=`. Received function ${r.name}`;else if(typeof r=="object"){var l;if((l=r.constructor)!==null&&l!==void 0&&l.name)i+=`. Received an instance of ${r.constructor.name}`;else{let u=ji(r,{depth:-1});i+=`. Received ${u}`}}else{let u=ji(r,{colors:!1});u.length>25&&(u=`${u.slice(0,25)}...`),i+=`. Received type ${typeof r} (${u})`}return i},TypeError);be("ERR_INVALID_ARG_VALUE",(t,e,r="is invalid")=>{let i=ji(e);return i.length>128&&(i=i.slice(0,128)+"..."),`The ${t.includes(".")?"property":"argument"} '${t}' ${r}. Received ${i}`},TypeError);be("ERR_INVALID_RETURN_VALUE",(t,e,r)=>{var i;let n=r!=null&&(i=r.constructor)!==null&&i!==void 0&&i.name?`instance of ${r.constructor.name}`:`type ${typeof r}`;return`Expected ${t} to be returned from the "${e}" function but got ${n}.`},TypeError);be("ERR_MISSING_ARGS",(...t)=>{Gt(t.length>0,"At least one arg needs to be specified");let e,r=t.length;switch(t=(Array.isArray(t)?t:[t]).map(i=>`"${i}"`).join(" or "),r){case 1:e+=`The ${t[0]} argument`;break;case 2:e+=`The ${t[0]} and ${t[1]} arguments`;break;default:{let i=t.pop();e+=`The ${t.join(", ")}, and ${i} arguments`}break}return`${e} must be specified`},TypeError);be("ERR_OUT_OF_RANGE",(t,e,r)=>{Gt(e,'Missing "range" argument');let i;return Number.isInteger(r)&&Math.abs(r)>2**32?i=Hu(String(r)):typeof r=="bigint"?(i=String(r),(r>2n**32n||r<-(2n**32n))&&(i=Hu(i)),i+="n"):i=ji(r),`The value of "${t}" is out of range. It must be ${e}. Received ${i}`},RangeError);be("ERR_MULTIPLE_CALLBACK","Callback called multiple times",Error);be("ERR_METHOD_NOT_IMPLEMENTED","The %s method is not implemented",Error);be("ERR_STREAM_ALREADY_FINISHED","Cannot call %s after a stream was finished",Error);be("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable",Error);be("ERR_STREAM_DESTROYED","Cannot call %s after a stream was destroyed",Error);be("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError);be("ERR_STREAM_PREMATURE_CLOSE","Premature close",Error);be("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF",Error);be("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event",Error);be("ERR_STREAM_WRITE_AFTER_END","write after end",Error);be("ERR_UNKNOWN_ENCODING","Unknown encoding: %s",TypeError);zu.exports={AbortError:ds,aggregateTwoErrors:Vu(Kg),hideStackFrames:Vu,codes:Fi}});var li=L((AA,tf)=>{"use strict";_();E();m();var{ArrayIsArray:gs,ArrayPrototypeIncludes:Yu,ArrayPrototypeJoin:Ju,ArrayPrototypeMap:Qg,NumberIsInteger:ys,NumberIsNaN:Gg,NumberMAX_SAFE_INTEGER:Yg,NumberMIN_SAFE_INTEGER:Jg,NumberParseInt:Xg,ObjectPrototypeHasOwnProperty:Zg,RegExpPrototypeExec:Xu,String:ey,StringPrototypeToUpperCase:ty,StringPrototypeTrim:ry}=ce(),{hideStackFrames:Me,codes:{ERR_SOCKET_BAD_PORT:iy,ERR_INVALID_ARG_TYPE:Ae,ERR_INVALID_ARG_VALUE:Pr,ERR_OUT_OF_RANGE:Yt,ERR_UNKNOWN_SIGNAL:Ku}}=Se(),{normalizeEncoding:ny}=Je(),{isAsyncFunction:sy,isArrayBufferView:oy}=Je().types,Qu={};function ly(t){return t===(t|0)}function ay(t){return t===t>>>0}var uy=/^[0-7]+$/,fy="must be a 32-bit unsigned integer or an octal string";function cy(t,e,r){if(typeof t>"u"&&(t=r),typeof t=="string"){if(Xu(uy,t)===null)throw new Pr(e,t,fy);t=Xg(t,8)}return Zu(t,e),t}var hy=Me((t,e,r=Jg,i=Yg)=>{if(typeof t!="number")throw new Ae(e,"number",t);if(!ys(t))throw new Yt(e,"an integer",t);if(t<r||t>i)throw new Yt(e,`>= ${r} && <= ${i}`,t)}),dy=Me((t,e,r=-2147483648,i=2147483647)=>{if(typeof t!="number")throw new Ae(e,"number",t);if(!ys(t))throw new Yt(e,"an integer",t);if(t<r||t>i)throw new Yt(e,`>= ${r} && <= ${i}`,t)}),Zu=Me((t,e,r=!1)=>{if(typeof t!="number")throw new Ae(e,"number",t);if(!ys(t))throw new Yt(e,"an integer",t);let i=r?1:0,n=4294967295;if(t<i||t>n)throw new Yt(e,`>= ${i} && <= ${n}`,t)});function bs(t,e){if(typeof t!="string")throw new Ae(e,"string",t)}function py(t,e,r=void 0,i){if(typeof t!="number")throw new Ae(e,"number",t);if(r!=null&&t<r||i!=null&&t>i||(r!=null||i!=null)&&Gg(t))throw new Yt(e,`${r!=null?`>= ${r}`:""}${r!=null&&i!=null?" && ":""}${i!=null?`<= ${i}`:""}`,t)}var gy=Me((t,e,r)=>{if(!Yu(r,t)){let n="must be one of: "+Ju(Qg(r,o=>typeof o=="string"?`'${o}'`:ey(o)),", ");throw new Pr(e,t,n)}});function ef(t,e){if(typeof t!="boolean")throw new Ae(e,"boolean",t)}function ps(t,e,r){return t==null||!Zg(t,e)?r:t[e]}var yy=Me((t,e,r=null)=>{let i=ps(r,"allowArray",!1),n=ps(r,"allowFunction",!1);if(!ps(r,"nullable",!1)&&t===null||!i&&gs(t)||typeof t!="object"&&(!n||typeof t!="function"))throw new Ae(e,"Object",t)}),by=Me((t,e)=>{if(t!=null&&typeof t!="object"&&typeof t!="function")throw new Ae(e,"a dictionary",t)}),ws=Me((t,e,r=0)=>{if(!gs(t))throw new Ae(e,"Array",t);if(t.length<r){let i=`must be longer than ${r}`;throw new Pr(e,t,i)}});function wy(t,e){ws(t,e);for(let r=0;r<t.length;r++)bs(t[r],`${e}[${r}]`)}function _y(t,e){ws(t,e);for(let r=0;r<t.length;r++)ef(t[r],`${e}[${r}]`)}function my(t,e="signal"){if(bs(t,e),Qu[t]===void 0)throw Qu[ty(t)]!==void 0?new Ku(t+" (signals must use all capital letters)"):new Ku(t)}var Ey=Me((t,e="buffer")=>{if(!oy(t))throw new Ae(e,["Buffer","TypedArray","DataView"],t)});function vy(t,e){let r=ny(e),i=t.length;if(r==="hex"&&i%2!==0)throw new Pr("encoding",e,`is invalid for data of length ${i}`)}function Sy(t,e="Port",r=!0){if(typeof t!="number"&&typeof t!="string"||typeof t=="string"&&ry(t).length===0||+t!==+t>>>0||t>65535||t===0&&!r)throw new iy(e,t,r);return t|0}var Ay=Me((t,e)=>{if(t!==void 0&&(t===null||typeof t!="object"||!("aborted"in t)))throw new Ae(e,"AbortSignal",t)}),Iy=Me((t,e)=>{if(typeof t!="function")throw new Ae(e,"Function",t)}),Ty=Me((t,e)=>{if(typeof t!="function"||sy(t))throw new Ae(e,"Function",t)}),Ry=Me((t,e)=>{if(t!==void 0)throw new Ae(e,"undefined",t)});function Cy(t,e,r){if(!Yu(r,t))throw new Ae(e,`('${Ju(r,"|")}')`,t)}var By=/^(?:<[^>]*>)(?:\s*;\s*[^;"\s]+(?:=(")?[^;"\s]*\1)?)*$/;function Gu(t,e){if(typeof t>"u"||!Xu(By,t))throw new Pr(e,t,'must be an array or string of format "</styles.css>; rel=preload; as=style"')}function Py(t){if(typeof t=="string")return Gu(t,"hints"),t;if(gs(t)){let e=t.length,r="";if(e===0)return r;for(let i=0;i<e;i++){let n=t[i];Gu(n,"hints"),r+=n,i!==e-1&&(r+=", ")}return r}throw new Pr("hints",t,'must be an array or string of format "</styles.css>; rel=preload; as=style"')}tf.exports={isInt32:ly,isUint32:ay,parseFileMode:cy,validateArray:ws,validateStringArray:wy,validateBooleanArray:_y,validateBoolean:ef,validateBuffer:Ey,validateDictionary:by,validateEncoding:vy,validateFunction:Iy,validateInt32:dy,validateInteger:hy,validateNumber:py,validateObject:yy,validateOneOf:gy,validatePlainFunction:Ty,validatePort:Sy,validateSignalName:my,validateString:bs,validateUint32:Zu,validateUndefined:Ry,validateUnion:Cy,validateAbortSignal:Ay,validateLinkHeaderValue:Py}});var Ut=L((xA,of)=>{_();E();m();var le=of.exports={},Xe,Ze;function _s(){throw new Error("setTimeout has not been defined")}function ms(){throw new Error("clearTimeout has not been defined")}(function(){try{typeof setTimeout=="function"?Xe=setTimeout:Xe=_s}catch{Xe=_s}try{typeof clearTimeout=="function"?Ze=clearTimeout:Ze=ms}catch{Ze=ms}})();function rf(t){if(Xe===setTimeout)return setTimeout(t,0);if((Xe===_s||!Xe)&&setTimeout)return Xe=setTimeout,setTimeout(t,0);try{return Xe(t,0)}catch{try{return Xe.call(null,t,0)}catch{return Xe.call(this,t,0)}}}function xy(t){if(Ze===clearTimeout)return clearTimeout(t);if((Ze===ms||!Ze)&&clearTimeout)return Ze=clearTimeout,clearTimeout(t);try{return Ze(t)}catch{try{return Ze.call(null,t)}catch{return Ze.call(this,t)}}}var wt=[],xr=!1,Jt,Wi=-1;function Oy(){!xr||!Jt||(xr=!1,Jt.length?wt=Jt.concat(wt):Wi=-1,wt.length&&nf())}function nf(){if(!xr){var t=rf(Oy);xr=!0;for(var e=wt.length;e;){for(Jt=wt,wt=[];++Wi<e;)Jt&&Jt[Wi].run();Wi=-1,e=wt.length}Jt=null,xr=!1,xy(t)}}le.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];wt.push(new sf(t,e)),wt.length===1&&!xr&&rf(nf)};function sf(t,e){this.fun=t,this.array=e}sf.prototype.run=function(){this.fun.apply(null,this.array)};le.title="browser";le.browser=!0;le.env={};le.argv=[];le.version="";le.versions={};function _t(){}le.on=_t;le.addListener=_t;le.once=_t;le.off=_t;le.removeListener=_t;le.removeAllListeners=_t;le.emit=_t;le.prependListener=_t;le.prependOnceListener=_t;le.listeners=function(t){return[]};le.binding=function(t){throw new Error("process.binding is not supported")};le.cwd=function(){return"/"};le.chdir=function(t){throw new Error("process.chdir is not supported")};le.umask=function(){return 0}});var tt=L((qA,vf)=>{"use strict";_();E();m();var{Symbol:$i,SymbolAsyncIterator:lf,SymbolIterator:af,SymbolFor:uf}=ce(),ff=$i("kDestroyed"),cf=$i("kIsErrored"),Es=$i("kIsReadable"),hf=$i("kIsDisturbed"),ky=uf("nodejs.webstream.isClosedPromise"),Ly=uf("nodejs.webstream.controllerErrorFunction");function Hi(t,e=!1){var r;return!!(t&&typeof t.pipe=="function"&&typeof t.on=="function"&&(!e||typeof t.pause=="function"&&typeof t.resume=="function")&&(!t._writableState||((r=t._readableState)===null||r===void 0?void 0:r.readable)!==!1)&&(!t._writableState||t._readableState))}function Vi(t){var e;return!!(t&&typeof t.write=="function"&&typeof t.on=="function"&&(!t._readableState||((e=t._writableState)===null||e===void 0?void 0:e.writable)!==!1))}function Uy(t){return!!(t&&typeof t.pipe=="function"&&t._readableState&&typeof t.on=="function"&&typeof t.write=="function")}function et(t){return t&&(t._readableState||t._writableState||typeof t.write=="function"&&typeof t.on=="function"||typeof t.pipe=="function"&&typeof t.on=="function")}function df(t){return!!(t&&!et(t)&&typeof t.pipeThrough=="function"&&typeof t.getReader=="function"&&typeof t.cancel=="function")}function pf(t){return!!(t&&!et(t)&&typeof t.getWriter=="function"&&typeof t.abort=="function")}function gf(t){return!!(t&&!et(t)&&typeof t.readable=="object"&&typeof t.writable=="object")}function My(t){return df(t)||pf(t)||gf(t)}function Ny(t,e){return t==null?!1:e===!0?typeof t[lf]=="function":e===!1?typeof t[af]=="function":typeof t[lf]=="function"||typeof t[af]=="function"}function zi(t){if(!et(t))return null;let e=t._writableState,r=t._readableState,i=e||r;return!!(t.destroyed||t[ff]||i!=null&&i.destroyed)}function yf(t){if(!Vi(t))return null;if(t.writableEnded===!0)return!0;let e=t._writableState;return e!=null&&e.errored?!1:typeof e?.ended!="boolean"?null:e.ended}function qy(t,e){if(!Vi(t))return null;if(t.writableFinished===!0)return!0;let r=t._writableState;return r!=null&&r.errored?!1:typeof r?.finished!="boolean"?null:!!(r.finished||e===!1&&r.ended===!0&&r.length===0)}function Dy(t){if(!Hi(t))return null;if(t.readableEnded===!0)return!0;let e=t._readableState;return!e||e.errored?!1:typeof e?.ended!="boolean"?null:e.ended}function bf(t,e){if(!Hi(t))return null;let r=t._readableState;return r!=null&&r.errored?!1:typeof r?.endEmitted!="boolean"?null:!!(r.endEmitted||e===!1&&r.ended===!0&&r.length===0)}function wf(t){return t&&t[Es]!=null?t[Es]:typeof t?.readable!="boolean"?null:zi(t)?!1:Hi(t)&&t.readable&&!bf(t)}function _f(t){return typeof t?.writable!="boolean"?null:zi(t)?!1:Vi(t)&&t.writable&&!yf(t)}function jy(t,e){return et(t)?zi(t)?!0:!(e?.readable!==!1&&wf(t)||e?.writable!==!1&&_f(t)):null}function Fy(t){var e,r;return et(t)?t.writableErrored?t.writableErrored:(e=(r=t._writableState)===null||r===void 0?void 0:r.errored)!==null&&e!==void 0?e:null:null}function Wy(t){var e,r;return et(t)?t.readableErrored?t.readableErrored:(e=(r=t._readableState)===null||r===void 0?void 0:r.errored)!==null&&e!==void 0?e:null:null}function $y(t){if(!et(t))return null;if(typeof t.closed=="boolean")return t.closed;let e=t._writableState,r=t._readableState;return typeof e?.closed=="boolean"||typeof r?.closed=="boolean"?e?.closed||r?.closed:typeof t._closed=="boolean"&&mf(t)?t._closed:null}function mf(t){return typeof t._closed=="boolean"&&typeof t._defaultKeepAlive=="boolean"&&typeof t._removedConnection=="boolean"&&typeof t._removedContLen=="boolean"}function Ef(t){return typeof t._sent100=="boolean"&&mf(t)}function Hy(t){var e;return typeof t._consuming=="boolean"&&typeof t._dumped=="boolean"&&((e=t.req)===null||e===void 0?void 0:e.upgradeOrConnect)===void 0}function Vy(t){if(!et(t))return null;let e=t._writableState,r=t._readableState,i=e||r;return!i&&Ef(t)||!!(i&&i.autoDestroy&&i.emitClose&&i.closed===!1)}function zy(t){var e;return!!(t&&((e=t[hf])!==null&&e!==void 0?e:t.readableDidRead||t.readableAborted))}function Ky(t){var e,r,i,n,o,s,l,u,c,h;return!!(t&&((e=(r=(i=(n=(o=(s=t[cf])!==null&&s!==void 0?s:t.readableErrored)!==null&&o!==void 0?o:t.writableErrored)!==null&&n!==void 0?n:(l=t._readableState)===null||l===void 0?void 0:l.errorEmitted)!==null&&i!==void 0?i:(u=t._writableState)===null||u===void 0?void 0:u.errorEmitted)!==null&&r!==void 0?r:(c=t._readableState)===null||c===void 0?void 0:c.errored)!==null&&e!==void 0?e:!((h=t._writableState)===null||h===void 0)&&h.errored))}vf.exports={kDestroyed:ff,isDisturbed:zy,kIsDisturbed:hf,isErrored:Ky,kIsErrored:cf,isReadable:wf,kIsReadable:Es,kIsClosedPromise:ky,kControllerErrorFunction:Ly,isClosed:$y,isDestroyed:zi,isDuplexNodeStream:Uy,isFinished:jy,isIterable:Ny,isReadableNodeStream:Hi,isReadableStream:df,isReadableEnded:Dy,isReadableFinished:bf,isReadableErrored:Wy,isNodeStream:et,isWebStream:My,isWritable:_f,isWritableNodeStream:Vi,isWritableStream:pf,isWritableEnded:yf,isWritableFinished:qy,isWritableErrored:Fy,isServerRequest:Hy,isServerResponse:Ef,willEmitClose:Vy,isTransformStream:gf}});var mt=L((VA,Ts)=>{_();E();m();var Mt=Ut(),{AbortError:xf,codes:Qy}=Se(),{ERR_INVALID_ARG_TYPE:Gy,ERR_STREAM_PREMATURE_CLOSE:Sf}=Qy,{kEmptyObject:Ss,once:As}=Je(),{validateAbortSignal:Yy,validateFunction:Jy,validateObject:Xy,validateBoolean:Zy}=li(),{Promise:eb,PromisePrototypeThen:tb}=ce(),{isClosed:rb,isReadable:Af,isReadableNodeStream:vs,isReadableStream:ib,isReadableFinished:If,isReadableErrored:Tf,isWritable:Rf,isWritableNodeStream:Cf,isWritableStream:nb,isWritableFinished:Bf,isWritableErrored:Pf,isNodeStream:sb,willEmitClose:ob,kIsClosedPromise:lb}=tt();function ab(t){return t.setHeader&&typeof t.abort=="function"}var Is=()=>{};function Of(t,e,r){var i,n;if(arguments.length===2?(r=e,e=Ss):e==null?e=Ss:Xy(e,"options"),Jy(r,"callback"),Yy(e.signal,"options.signal"),r=As(r),ib(t)||nb(t))return ub(t,e,r);if(!sb(t))throw new Gy("stream",["ReadableStream","WritableStream","Stream"],t);let o=(i=e.readable)!==null&&i!==void 0?i:vs(t),s=(n=e.writable)!==null&&n!==void 0?n:Cf(t),l=t._writableState,u=t._readableState,c=()=>{t.writable||g()},h=ob(t)&&vs(t)===o&&Cf(t)===s,d=Bf(t,!1),g=()=>{d=!0,t.destroyed&&(h=!1),!(h&&(!t.readable||o))&&(!o||y)&&r.call(t)},y=If(t,!1),w=()=>{y=!0,t.destroyed&&(h=!1),!(h&&(!t.writable||s))&&(!s||d)&&r.call(t)},S=N=>{r.call(t,N)},A=rb(t),I=()=>{A=!0;let N=Pf(t)||Tf(t);if(N&&typeof N!="boolean")return r.call(t,N);if(o&&!y&&vs(t,!0)&&!If(t,!1))return r.call(t,new Sf);if(s&&!d&&!Bf(t,!1))return r.call(t,new Sf);r.call(t)},P=()=>{A=!0;let N=Pf(t)||Tf(t);if(N&&typeof N!="boolean")return r.call(t,N);r.call(t)},R=()=>{t.req.on("finish",g)};ab(t)?(t.on("complete",g),h||t.on("abort",I),t.req?R():t.on("request",R)):s&&!l&&(t.on("end",c),t.on("close",c)),!h&&typeof t.aborted=="boolean"&&t.on("aborted",I),t.on("end",w),t.on("finish",g),e.error!==!1&&t.on("error",S),t.on("close",I),A?Mt.nextTick(I):l!=null&&l.errorEmitted||u!=null&&u.errorEmitted?h||Mt.nextTick(P):(!o&&(!h||Af(t))&&(d||Rf(t)===!1)||!s&&(!h||Rf(t))&&(y||Af(t)===!1)||u&&t.req&&t.aborted)&&Mt.nextTick(P);let M=()=>{r=Is,t.removeListener("aborted",I),t.removeListener("complete",g),t.removeListener("abort",I),t.removeListener("request",R),t.req&&t.req.removeListener("finish",g),t.removeListener("end",c),t.removeListener("close",c),t.removeListener("finish",g),t.removeListener("end",w),t.removeListener("error",S),t.removeListener("close",I)};if(e.signal&&!A){let N=()=>{let V=r;M(),V.call(t,new xf(void 0,{cause:e.signal.reason}))};if(e.signal.aborted)Mt.nextTick(N);else{let V=r;r=As((...Q)=>{e.signal.removeEventListener("abort",N),V.apply(t,Q)}),e.signal.addEventListener("abort",N)}}return M}function ub(t,e,r){let i=!1,n=Is;if(e.signal)if(n=()=>{i=!0,r.call(t,new xf(void 0,{cause:e.signal.reason}))},e.signal.aborted)Mt.nextTick(n);else{let s=r;r=As((...l)=>{e.signal.removeEventListener("abort",n),s.apply(t,l)}),e.signal.addEventListener("abort",n)}let o=(...s)=>{i||Mt.nextTick(()=>r.apply(t,s))};return tb(t[lb].promise,o,o),Is}function fb(t,e){var r;let i=!1;return e===null&&(e=Ss),(r=e)!==null&&r!==void 0&&r.cleanup&&(Zy(e.cleanup,"cleanup"),i=e.cleanup),new eb((n,o)=>{let s=Of(t,e,l=>{i&&s(),l?o(l):n()})})}Ts.exports=Of;Ts.exports.finished=fb});var Xt=L((XA,jf)=>{"use strict";_();E();m();var rt=Ut(),{aggregateTwoErrors:cb,codes:{ERR_MULTIPLE_CALLBACK:hb},AbortError:db}=Se(),{Symbol:Uf}=ce(),{kDestroyed:pb,isDestroyed:gb,isFinished:yb,isServerRequest:bb}=tt(),Mf=Uf("kDestroy"),Rs=Uf("kConstruct");function Nf(t,e,r){t&&(t.stack,e&&!e.errored&&(e.errored=t),r&&!r.errored&&(r.errored=t))}function wb(t,e){let r=this._readableState,i=this._writableState,n=i||r;return i!=null&&i.destroyed||r!=null&&r.destroyed?(typeof e=="function"&&e(),this):(Nf(t,i,r),i&&(i.destroyed=!0),r&&(r.destroyed=!0),n.constructed?kf(this,t,e):this.once(Mf,function(o){kf(this,cb(o,t),e)}),this)}function kf(t,e,r){let i=!1;function n(o){if(i)return;i=!0;let s=t._readableState,l=t._writableState;Nf(o,l,s),l&&(l.closed=!0),s&&(s.closed=!0),typeof r=="function"&&r(o),o?rt.nextTick(_b,t,o):rt.nextTick(qf,t)}try{t._destroy(e||null,n)}catch(o){n(o)}}function _b(t,e){Cs(t,e),qf(t)}function qf(t){let e=t._readableState,r=t._writableState;r&&(r.closeEmitted=!0),e&&(e.closeEmitted=!0),(r!=null&&r.emitClose||e!=null&&e.emitClose)&&t.emit("close")}function Cs(t,e){let r=t._readableState,i=t._writableState;i!=null&&i.errorEmitted||r!=null&&r.errorEmitted||(i&&(i.errorEmitted=!0),r&&(r.errorEmitted=!0),t.emit("error",e))}function mb(){let t=this._readableState,e=this._writableState;t&&(t.constructed=!0,t.closed=!1,t.closeEmitted=!1,t.destroyed=!1,t.errored=null,t.errorEmitted=!1,t.reading=!1,t.ended=t.readable===!1,t.endEmitted=t.readable===!1),e&&(e.constructed=!0,e.destroyed=!1,e.closed=!1,e.closeEmitted=!1,e.errored=null,e.errorEmitted=!1,e.finalCalled=!1,e.prefinished=!1,e.ended=e.writable===!1,e.ending=e.writable===!1,e.finished=e.writable===!1)}function Bs(t,e,r){let i=t._readableState,n=t._writableState;if(n!=null&&n.destroyed||i!=null&&i.destroyed)return this;i!=null&&i.autoDestroy||n!=null&&n.autoDestroy?t.destroy(e):e&&(e.stack,n&&!n.errored&&(n.errored=e),i&&!i.errored&&(i.errored=e),r?rt.nextTick(Cs,t,e):Cs(t,e))}function Eb(t,e){if(typeof t._construct!="function")return;let r=t._readableState,i=t._writableState;r&&(r.constructed=!1),i&&(i.constructed=!1),t.once(Rs,e),!(t.listenerCount(Rs)>1)&&rt.nextTick(vb,t)}function vb(t){let e=!1;function r(i){if(e){Bs(t,i??new hb);return}e=!0;let n=t._readableState,o=t._writableState,s=o||n;n&&(n.constructed=!0),o&&(o.constructed=!0),s.destroyed?t.emit(Mf,i):i?Bs(t,i,!0):rt.nextTick(Sb,t)}try{t._construct(i=>{rt.nextTick(r,i)})}catch(i){rt.nextTick(r,i)}}function Sb(t){t.emit(Rs)}function Lf(t){return t?.setHeader&&typeof t.abort=="function"}function Df(t){t.emit("close")}function Ab(t,e){t.emit("error",e),rt.nextTick(Df,t)}function Ib(t,e){!t||gb(t)||(!e&&!yb(t)&&(e=new db),bb(t)?(t.socket=null,t.destroy(e)):Lf(t)?t.abort():Lf(t.req)?t.req.abort():typeof t.destroy=="function"?t.destroy(e):typeof t.close=="function"?t.close():e?rt.nextTick(Ab,t,e):rt.nextTick(Df,t),t.destroyed||(t[pb]=!0))}jf.exports={construct:Eb,destroyer:Ib,destroy:wb,undestroy:mb,errorOrDestroy:Bs}});function G(){G.init.call(this)}function Ki(t){if(typeof t!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}function Yf(t){return t._maxListeners===void 0?G.defaultMaxListeners:t._maxListeners}function Hf(t,e,r,i){var n,o,s,l;if(Ki(r),(o=t._events)===void 0?(o=t._events=Object.create(null),t._eventsCount=0):(o.newListener!==void 0&&(t.emit("newListener",e,r.listener?r.listener:r),o=t._events),s=o[e]),s===void 0)s=o[e]=r,++t._eventsCount;else if(typeof s=="function"?s=o[e]=i?[r,s]:[s,r]:i?s.unshift(r):s.push(r),(n=Yf(t))>0&&s.length>n&&!s.warned){s.warned=!0;var u=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");u.name="MaxListenersExceededWarning",u.emitter=t,u.type=e,u.count=s.length,l=u,console&&console.warn&&console.warn(l)}return t}function Tb(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function Vf(t,e,r){var i={fired:!1,wrapFn:void 0,target:t,type:e,listener:r},n=Tb.bind(i);return n.listener=r,i.wrapFn=n,n}function zf(t,e,r){var i=t._events;if(i===void 0)return[];var n=i[e];return n===void 0?[]:typeof n=="function"?r?[n.listener||n]:[n]:r?function(o){for(var s=new Array(o.length),l=0;l<s.length;++l)s[l]=o[l].listener||o[l];return s}(n):Jf(n,n.length)}function Kf(t){var e=this._events;if(e!==void 0){var r=e[t];if(typeof r=="function")return 1;if(r!==void 0)return r.length}return 0}function Jf(t,e){for(var r=new Array(e),i=0;i<e;++i)r[i]=t[i];return r}var Qf,Gf,Or,Ff,Wf,$f,Be,Ps=ge(()=>{_();E();m();Or=typeof Reflect=="object"?Reflect:null,Ff=Or&&typeof Or.apply=="function"?Or.apply:function(t,e,r){return Function.prototype.apply.call(t,e,r)};Gf=Or&&typeof Or.ownKeys=="function"?Or.ownKeys:Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:function(t){return Object.getOwnPropertyNames(t)};Wf=Number.isNaN||function(t){return t!=t};Qf=G,G.EventEmitter=G,G.prototype._events=void 0,G.prototype._eventsCount=0,G.prototype._maxListeners=void 0;$f=10;Object.defineProperty(G,"defaultMaxListeners",{enumerable:!0,get:function(){return $f},set:function(t){if(typeof t!="number"||t<0||Wf(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");$f=t}}),G.init=function(){this._events!==void 0&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},G.prototype.setMaxListeners=function(t){if(typeof t!="number"||t<0||Wf(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this},G.prototype.getMaxListeners=function(){return Yf(this)},G.prototype.emit=function(t){for(var e=[],r=1;r<arguments.length;r++)e.push(arguments[r]);var i=t==="error",n=this._events;if(n!==void 0)i=i&&n.error===void 0;else if(!i)return!1;if(i){var o;if(e.length>0&&(o=e[0]),o instanceof Error)throw o;var s=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw s.context=o,s}var l=n[t];if(l===void 0)return!1;if(typeof l=="function")Ff(l,this,e);else{var u=l.length,c=Jf(l,u);for(r=0;r<u;++r)Ff(c[r],this,e)}return!0},G.prototype.addListener=function(t,e){return Hf(this,t,e,!1)},G.prototype.on=G.prototype.addListener,G.prototype.prependListener=function(t,e){return Hf(this,t,e,!0)},G.prototype.once=function(t,e){return Ki(e),this.on(t,Vf(this,t,e)),this},G.prototype.prependOnceListener=function(t,e){return Ki(e),this.prependListener(t,Vf(this,t,e)),this},G.prototype.removeListener=function(t,e){var r,i,n,o,s;if(Ki(e),(i=this._events)===void 0)return this;if((r=i[t])===void 0)return this;if(r===e||r.listener===e)--this._eventsCount==0?this._events=Object.create(null):(delete i[t],i.removeListener&&this.emit("removeListener",t,r.listener||e));else if(typeof r!="function"){for(n=-1,o=r.length-1;o>=0;o--)if(r[o]===e||r[o].listener===e){s=r[o].listener,n=o;break}if(n<0)return this;n===0?r.shift():function(l,u){for(;u+1<l.length;u++)l[u]=l[u+1];l.pop()}(r,n),r.length===1&&(i[t]=r[0]),i.removeListener!==void 0&&this.emit("removeListener",t,s||e)}return this},G.prototype.off=G.prototype.removeListener,G.prototype.removeAllListeners=function(t){var e,r,i;if((r=this._events)===void 0)return this;if(r.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):r[t]!==void 0&&(--this._eventsCount==0?this._events=Object.create(null):delete r[t]),this;if(arguments.length===0){var n,o=Object.keys(r);for(i=0;i<o.length;++i)(n=o[i])!=="removeListener"&&this.removeAllListeners(n);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(typeof(e=r[t])=="function")this.removeListener(t,e);else if(e!==void 0)for(i=e.length-1;i>=0;i--)this.removeListener(t,e[i]);return this},G.prototype.listeners=function(t){return zf(this,t,!0)},G.prototype.rawListeners=function(t){return zf(this,t,!1)},G.listenerCount=function(t,e){return typeof t.listenerCount=="function"?t.listenerCount(e):Kf.call(t,e)},G.prototype.listenerCount=Kf,G.prototype.eventNames=function(){return this._eventsCount>0?Gf(this._events):[]};Be=Qf;Be.EventEmitter;Be.defaultMaxListeners;Be.init;Be.listenerCount;Be.EventEmitter;Be.defaultMaxListeners;Be.init;Be.listenerCount});var Zt={};zt(Zt,{EventEmitter:()=>Rb,default:()=>Be,defaultMaxListeners:()=>Cb,init:()=>Bb,listenerCount:()=>Pb,on:()=>xb,once:()=>Ob});var Rb,Cb,Bb,Pb,xb,Ob,er=ge(()=>{_();E();m();Ps();Ps();Be.once=function(t,e){return new Promise((r,i)=>{function n(...s){o!==void 0&&t.removeListener("error",o),r(s)}let o;e!=="error"&&(o=s=>{t.removeListener(name,n),i(s)},t.once("error",o)),t.once(e,n)})};Be.on=function(t,e){let r=[],i=[],n=null,o=!1,s={async next(){let c=r.shift();if(c)return createIterResult(c,!1);if(n){let h=Promise.reject(n);return n=null,h}return o?createIterResult(void 0,!0):new Promise((h,d)=>i.push({resolve:h,reject:d}))},async return(){t.removeListener(e,l),t.removeListener("error",u),o=!0;for(let c of i)c.resolve(createIterResult(void 0,!0));return createIterResult(void 0,!0)},throw(c){n=c,t.removeListener(e,l),t.removeListener("error",u)},[Symbol.asyncIterator](){return this}};return t.on(e,l),t.on("error",u),s;function l(...c){let h=i.shift();h?h.resolve(createIterResult(c,!1)):r.push(c)}function u(c){o=!0;let h=i.shift();h?h.reject(c):n=c,s.return()}};({EventEmitter:Rb,defaultMaxListeners:Cb,init:Bb,listenerCount:Pb,on:xb,once:Ob}=Be)});var Yi=L((mI,Zf)=>{"use strict";_();E();m();var{ArrayIsArray:kb,ObjectSetPrototypeOf:Xf}=ce(),{EventEmitter:Qi}=(er(),Z(Zt));function Gi(t){Qi.call(this,t)}Xf(Gi.prototype,Qi.prototype);Xf(Gi,Qi);Gi.prototype.pipe=function(t,e){let r=this;function i(h){t.writable&&t.write(h)===!1&&r.pause&&r.pause()}r.on("data",i);function n(){r.readable&&r.resume&&r.resume()}t.on("drain",n),!t._isStdio&&(!e||e.end!==!1)&&(r.on("end",s),r.on("close",l));let o=!1;function s(){o||(o=!0,t.end())}function l(){o||(o=!0,typeof t.destroy=="function"&&t.destroy())}function u(h){c(),Qi.listenerCount(this,"error")===0&&this.emit("error",h)}xs(r,"error",u),xs(t,"error",u);function c(){r.removeListener("data",i),t.removeListener("drain",n),r.removeListener("end",s),r.removeListener("close",l),r.removeListener("error",u),t.removeListener("error",u),r.removeListener("end",c),r.removeListener("close",c),t.removeListener("close",c)}return r.on("end",c),r.on("close",c),t.on("close",c),t.emit("pipe",r),t};function xs(t,e,r){if(typeof t.prependListener=="function")return t.prependListener(e,r);!t._events||!t._events[e]?t.on(e,r):kb(t._events[e])?t._events[e].unshift(r):t._events[e]=[r,t._events[e]]}Zf.exports={Stream:Gi,prependListener:xs}});var ai=L((RI,Ji)=>{"use strict";_();E();m();var{AbortError:ec,codes:Lb}=Se(),{isNodeStream:tc,isWebStream:Ub,kControllerErrorFunction:Mb}=tt(),Nb=mt(),{ERR_INVALID_ARG_TYPE:rc}=Lb,qb=(t,e)=>{if(typeof t!="object"||!("aborted"in t))throw new rc(e,"AbortSignal",t)};Ji.exports.addAbortSignal=function(e,r){if(qb(e,"signal"),!tc(r)&&!Ub(r))throw new rc("stream",["ReadableStream","WritableStream","Stream"],r);return Ji.exports.addAbortSignalNoValidate(e,r)};Ji.exports.addAbortSignalNoValidate=function(t,e){if(typeof t!="object"||!("aborted"in t))return e;let r=tc(e)?()=>{e.destroy(new ec(void 0,{cause:t.reason}))}:()=>{e[Mb](new ec(void 0,{cause:t.reason}))};return t.aborted?r():(t.addEventListener("abort",r),Nb(e,()=>t.removeEventListener("abort",r))),e}});var sc=L((UI,nc)=>{"use strict";_();E();m();var{StringPrototypeSlice:ic,SymbolIterator:Db,TypedArrayPrototypeSet:Xi,Uint8Array:jb}=ce(),{Buffer:Os}=(ye(),Z(me)),{inspect:Fb}=Je();nc.exports=class{constructor(){this.head=null,this.tail=null,this.length=0}push(e){let r={data:e,next:null};this.length>0?this.tail.next=r:this.head=r,this.tail=r,++this.length}unshift(e){let r={data:e,next:this.head};this.length===0&&(this.tail=r),this.head=r,++this.length}shift(){if(this.length===0)return;let e=this.head.data;return this.length===1?this.head=this.tail=null:this.head=this.head.next,--this.length,e}clear(){this.head=this.tail=null,this.length=0}join(e){if(this.length===0)return"";let r=this.head,i=""+r.data;for(;(r=r.next)!==null;)i+=e+r.data;return i}concat(e){if(this.length===0)return Os.alloc(0);let r=Os.allocUnsafe(e>>>0),i=this.head,n=0;for(;i;)Xi(r,i.data,n),n+=i.data.length,i=i.next;return r}consume(e,r){let i=this.head.data;if(e<i.length){let n=i.slice(0,e);return this.head.data=i.slice(e),n}return e===i.length?this.shift():r?this._getString(e):this._getBuffer(e)}first(){return this.head.data}*[Db](){for(let e=this.head;e;e=e.next)yield e.data}_getString(e){let r="",i=this.head,n=0;do{let o=i.data;if(e>o.length)r+=o,e-=o.length;else{e===o.length?(r+=o,++n,i.next?this.head=i.next:this.head=this.tail=null):(r+=ic(o,0,e),this.head=i,i.data=ic(o,e));break}++n}while((i=i.next)!==null);return this.length-=n,r}_getBuffer(e){let r=Os.allocUnsafe(e),i=e,n=this.head,o=0;do{let s=n.data;if(e>s.length)Xi(r,s,i-e),e-=s.length;else{e===s.length?(Xi(r,s,i-e),++o,n.next?this.head=n.next:this.head=this.tail=null):(Xi(r,new jb(s.buffer,s.byteOffset,e),i-e),this.head=n,n.data=s.slice(e));break}++o}while((n=n.next)!==null);return this.length-=o,r}[Symbol.for("nodejs.util.inspect.custom")](e,r){return Fb(this,{...r,depth:0,customInspect:!1})}}});var Zi=L((WI,lc)=>{"use strict";_();E();m();var{MathFloor:Wb,NumberIsInteger:$b}=ce(),{ERR_INVALID_ARG_VALUE:Hb}=Se().codes;function Vb(t,e,r){return t.highWaterMark!=null?t.highWaterMark:e?t[r]:null}function oc(t){return t?16:16*1024}function zb(t,e,r,i){let n=Vb(e,i,r);if(n!=null){if(!$b(n)||n<0){let o=i?`options.${r}`:"options.highWaterMark";throw new Hb(o,n)}return Wb(n)}return oc(t.objectMode)}lc.exports={getHighWaterMark:zb,getDefaultHighWaterMark:oc}});function fc(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");return r===-1&&(r=e),[r,r===e?0:4-r%4]}function Kb(t,e,r){for(var i,n,o=[],s=e;s<r;s+=3)i=(t[s]<<16&16711680)+(t[s+1]<<8&65280)+(255&t[s+2]),o.push($e[(n=i)>>18&63]+$e[n>>12&63]+$e[n>>6&63]+$e[63&n]);return o.join("")}function Et(t){if(t>2147483647)throw new RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,O.prototype),e}function O(t,e,r){if(typeof t=="number"){if(typeof e=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return Ms(t)}return mc(t,e,r)}function mc(t,e,r){if(typeof t=="string")return function(o,s){if(typeof s=="string"&&s!==""||(s="utf8"),!O.isEncoding(s))throw new TypeError("Unknown encoding: "+s);var l=0|vc(o,s),u=Et(l),c=u.write(o,s);return c!==l&&(u=u.slice(0,c)),u}(t,e);if(ArrayBuffer.isView(t))return ks(t);if(t==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(vt(t,ArrayBuffer)||t&&vt(t.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(vt(t,SharedArrayBuffer)||t&&vt(t.buffer,SharedArrayBuffer)))return hc(t,e,r);if(typeof t=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');var i=t.valueOf&&t.valueOf();if(i!=null&&i!==t)return O.from(i,e,r);var n=function(o){if(O.isBuffer(o)){var s=0|Ds(o.length),l=Et(s);return l.length===0||o.copy(l,0,0,s),l}if(o.length!==void 0)return typeof o.length!="number"||js(o.length)?Et(0):ks(o);if(o.type==="Buffer"&&Array.isArray(o.data))return ks(o.data)}(t);if(n)return n;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof t[Symbol.toPrimitive]=="function")return O.from(t[Symbol.toPrimitive]("string"),e,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function Ec(t){if(typeof t!="number")throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function Ms(t){return Ec(t),Et(t<0?0:0|Ds(t))}function ks(t){for(var e=t.length<0?0:0|Ds(t.length),r=Et(e),i=0;i<e;i+=1)r[i]=255&t[i];return r}function hc(t,e,r){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw new RangeError('"length" is outside of buffer bounds');var i;return i=e===void 0&&r===void 0?new Uint8Array(t):r===void 0?new Uint8Array(t,e):new Uint8Array(t,e,r),Object.setPrototypeOf(i,O.prototype),i}function Ds(t){if(t>=2147483647)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+2147483647 .toString(16)+" bytes");return 0|t}function vc(t,e){if(O.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||vt(t,ArrayBuffer))return t.byteLength;if(typeof t!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,i=arguments.length>2&&arguments[2]===!0;if(!i&&r===0)return 0;for(var n=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return Ns(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return Tc(t).length;default:if(n)return i?-1:Ns(t).length;e=(""+e).toLowerCase(),n=!0}}function Gb(t,e,r){var i=!1;if((e===void 0||e<0)&&(e=0),e>this.length||((r===void 0||r>this.length)&&(r=this.length),r<=0)||(r>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return nw(this,e,r);case"utf8":case"utf-8":return Ac(this,e,r);case"ascii":return rw(this,e,r);case"latin1":case"binary":return iw(this,e,r);case"base64":return tw(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return sw(this,e,r);default:if(i)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),i=!0}}function rr(t,e,r){var i=t[e];t[e]=t[r],t[r]=i}function dc(t,e,r,i,n){if(t.length===0)return-1;if(typeof r=="string"?(i=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),js(r=+r)&&(r=n?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(n)return-1;r=t.length-1}else if(r<0){if(!n)return-1;r=0}if(typeof e=="string"&&(e=O.from(e,i)),O.isBuffer(e))return e.length===0?-1:pc(t,e,r,i,n);if(typeof e=="number")return e&=255,typeof Uint8Array.prototype.indexOf=="function"?n?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):pc(t,[e],r,i,n);throw new TypeError("val must be string, number or Buffer")}function pc(t,e,r,i,n){var o,s=1,l=t.length,u=e.length;if(i!==void 0&&((i=String(i).toLowerCase())==="ucs2"||i==="ucs-2"||i==="utf16le"||i==="utf-16le")){if(t.length<2||e.length<2)return-1;s=2,l/=2,u/=2,r/=2}function c(y,w){return s===1?y[w]:y.readUInt16BE(w*s)}if(n){var h=-1;for(o=r;o<l;o++)if(c(t,o)===c(e,h===-1?0:o-h)){if(h===-1&&(h=o),o-h+1===u)return h*s}else h!==-1&&(o-=o-h),h=-1}else for(r+u>l&&(r=l-u),o=r;o>=0;o--){for(var d=!0,g=0;g<u;g++)if(c(t,o+g)!==c(e,g)){d=!1;break}if(d)return o}return-1}function Yb(t,e,r,i){r=Number(r)||0;var n=t.length-r;i?(i=Number(i))>n&&(i=n):i=n;var o=e.length;i>o/2&&(i=o/2);for(var s=0;s<i;++s){var l=parseInt(e.substr(2*s,2),16);if(js(l))return s;t[r+s]=l}return s}function Jb(t,e,r,i){return nn(Ns(e,t.length-r),t,r,i)}function Sc(t,e,r,i){return nn(function(n){for(var o=[],s=0;s<n.length;++s)o.push(255&n.charCodeAt(s));return o}(e),t,r,i)}function Xb(t,e,r,i){return Sc(t,e,r,i)}function Zb(t,e,r,i){return nn(Tc(e),t,r,i)}function ew(t,e,r,i){return nn(function(n,o){for(var s,l,u,c=[],h=0;h<n.length&&!((o-=2)<0);++h)s=n.charCodeAt(h),l=s>>8,u=s%256,c.push(u),c.push(l);return c}(e,t.length-r),t,r,i)}function tw(t,e,r){return e===0&&r===t.length?Us.fromByteArray(t):Us.fromByteArray(t.slice(e,r))}function Ac(t,e,r){r=Math.min(t.length,r);for(var i=[],n=e;n<r;){var o,s,l,u,c=t[n],h=null,d=c>239?4:c>223?3:c>191?2:1;if(n+d<=r)switch(d){case 1:c<128&&(h=c);break;case 2:(192&(o=t[n+1]))==128&&(u=(31&c)<<6|63&o)>127&&(h=u);break;case 3:o=t[n+1],s=t[n+2],(192&o)==128&&(192&s)==128&&(u=(15&c)<<12|(63&o)<<6|63&s)>2047&&(u<55296||u>57343)&&(h=u);break;case 4:o=t[n+1],s=t[n+2],l=t[n+3],(192&o)==128&&(192&s)==128&&(192&l)==128&&(u=(15&c)<<18|(63&o)<<12|(63&s)<<6|63&l)>65535&&u<1114112&&(h=u)}h===null?(h=65533,d=1):h>65535&&(h-=65536,i.push(h>>>10&1023|55296),h=56320|1023&h),i.push(h),n+=d}return function(g){var y=g.length;if(y<=4096)return String.fromCharCode.apply(String,g);for(var w="",S=0;S<y;)w+=String.fromCharCode.apply(String,g.slice(S,S+=4096));return w}(i)}function rw(t,e,r){var i="";r=Math.min(t.length,r);for(var n=e;n<r;++n)i+=String.fromCharCode(127&t[n]);return i}function iw(t,e,r){var i="";r=Math.min(t.length,r);for(var n=e;n<r;++n)i+=String.fromCharCode(t[n]);return i}function nw(t,e,r){var i=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>i)&&(r=i);for(var n="",o=e;o<r;++o)n+=lw[t[o]];return n}function sw(t,e,r){for(var i=t.slice(e,r),n="",o=0;o<i.length;o+=2)n+=String.fromCharCode(i[o]+256*i[o+1]);return n}function pe(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}function Pe(t,e,r,i,n,o){if(!O.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>n||e<o)throw new RangeError('"value" argument is out of bounds');if(r+i>t.length)throw new RangeError("Index out of range")}function Ic(t,e,r,i,n,o){if(r+i>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function gc(t,e,r,i,n){return e=+e,r>>>=0,n||Ic(t,0,r,4),kr.write(t,e,r,i,23,4),r+4}function yc(t,e,r,i,n){return e=+e,r>>>=0,n||Ic(t,0,r,8),kr.write(t,e,r,i,52,8),r+8}function Ns(t,e){var r;e=e||1/0;for(var i=t.length,n=null,o=[],s=0;s<i;++s){if((r=t.charCodeAt(s))>55295&&r<57344){if(!n){if(r>56319){(e-=3)>-1&&o.push(239,191,189);continue}if(s+1===i){(e-=3)>-1&&o.push(239,191,189);continue}n=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),n=r;continue}r=65536+(n-55296<<10|r-56320)}else n&&(e-=3)>-1&&o.push(239,191,189);if(n=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function Tc(t){return Us.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(ow,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(t))}function nn(t,e,r,i){for(var n=0;n<i&&!(n+r>=e.length||n>=t.length);++n)e[n+r]=t[n];return n}function vt(t,e){return t instanceof e||t!=null&&t.constructor!=null&&t.constructor.name!=null&&t.constructor.name===e.name}function js(t){return t!=t}function bc(t,e){for(var r in t)e[r]=t[r]}function ir(t,e,r){return it(t,e,r)}function ui(t){var e;switch(this.encoding=function(r){var i=function(n){if(!n)return"utf8";for(var o;;)switch(n){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return n;default:if(o)return;n=(""+n).toLowerCase(),o=!0}}(r);if(typeof i!="string"&&(qs.isEncoding===wc||!wc(r)))throw new Error("Unknown encoding: "+r);return i||r}(t),this.encoding){case"utf16le":this.text=fw,this.end=cw,e=4;break;case"utf8":this.fillLast=uw,e=4;break;case"base64":this.text=hw,this.end=dw,e=3;break;default:return this.write=pw,this.end=gw,void 0}this.lastNeed=0,this.lastTotal=0,this.lastChar=qs.allocUnsafe(e)}function Ls(t){return t<=127?0:t>>5==6?2:t>>4==14?3:t>>3==30?4:t>>6==2?-1:-2}function uw(t){var e=this.lastTotal-this.lastNeed,r=function(i,n,o){if((192&n[0])!=128)return i.lastNeed=0,"\uFFFD";if(i.lastNeed>1&&n.length>1){if((192&n[1])!=128)return i.lastNeed=1,"\uFFFD";if(i.lastNeed>2&&n.length>2&&(192&n[2])!=128)return i.lastNeed=2,"\uFFFD"}}(this,t);return r!==void 0?r:this.lastNeed<=t.length?(t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(t.copy(this.lastChar,e,0,t.length),this.lastNeed-=t.length,void 0)}function fw(t,e){if((t.length-e)%2==0){var r=t.toString("utf16le",e);if(r){var i=r.charCodeAt(r.length-1);if(i>=55296&&i<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1)}function cw(t){var e=t&&t.length?this.write(t):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,r)}return e}function hw(t,e){var r=(t.length-e)%3;return r===0?t.toString("base64",e):(this.lastNeed=3-r,this.lastTotal=3,r===1?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-r))}function dw(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function pw(t){return t.toString(this.encoding)}function gw(t){return t&&t.length?this.write(t):""}var _c,$e,Oe,ac,en,tr,uc,Qb,St,Us,kr,cc,ow,lw,tn,rn,it,aw,nr,qs,wc,Fs=ge(()=>{_();E();m();for(_c={byteLength:function(t){var e=fc(t),r=e[0],i=e[1];return 3*(r+i)/4-i},toByteArray:function(t){var e,r,i=fc(t),n=i[0],o=i[1],s=new ac(function(c,h,d){return 3*(h+d)/4-d}(0,n,o)),l=0,u=o>0?n-4:n;for(r=0;r<u;r+=4)e=Oe[t.charCodeAt(r)]<<18|Oe[t.charCodeAt(r+1)]<<12|Oe[t.charCodeAt(r+2)]<<6|Oe[t.charCodeAt(r+3)],s[l++]=e>>16&255,s[l++]=e>>8&255,s[l++]=255&e;return o===2&&(e=Oe[t.charCodeAt(r)]<<2|Oe[t.charCodeAt(r+1)]>>4,s[l++]=255&e),o===1&&(e=Oe[t.charCodeAt(r)]<<10|Oe[t.charCodeAt(r+1)]<<4|Oe[t.charCodeAt(r+2)]>>2,s[l++]=e>>8&255,s[l++]=255&e),s},fromByteArray:function(t){for(var e,r=t.length,i=r%3,n=[],o=0,s=r-i;o<s;o+=16383)n.push(Kb(t,o,o+16383>s?s:o+16383));return i===1?(e=t[r-1],n.push($e[e>>2]+$e[e<<4&63]+"==")):i===2&&(e=(t[r-2]<<8)+t[r-1],n.push($e[e>>10]+$e[e>>4&63]+$e[e<<2&63]+"=")),n.join("")}},$e=[],Oe=[],ac=typeof Uint8Array<"u"?Uint8Array:Array,en="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",tr=0,uc=en.length;tr<uc;++tr)$e[tr]=en[tr],Oe[en.charCodeAt(tr)]=tr;Oe["-".charCodeAt(0)]=62,Oe["_".charCodeAt(0)]=63;Qb={read:function(t,e,r,i,n){var o,s,l=8*n-i-1,u=(1<<l)-1,c=u>>1,h=-7,d=r?n-1:0,g=r?-1:1,y=t[e+d];for(d+=g,o=y&(1<<-h)-1,y>>=-h,h+=l;h>0;o=256*o+t[e+d],d+=g,h-=8);for(s=o&(1<<-h)-1,o>>=-h,h+=i;h>0;s=256*s+t[e+d],d+=g,h-=8);if(o===0)o=1-c;else{if(o===u)return s?NaN:1/0*(y?-1:1);s+=Math.pow(2,i),o-=c}return(y?-1:1)*s*Math.pow(2,o-i)},write:function(t,e,r,i,n,o){var s,l,u,c=8*o-n-1,h=(1<<c)-1,d=h>>1,g=n===23?Math.pow(2,-24)-Math.pow(2,-77):0,y=i?0:o-1,w=i?1:-1,S=e<0||e===0&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(l=isNaN(e)?1:0,s=h):(s=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-s))<1&&(s--,u*=2),(e+=s+d>=1?g/u:g*Math.pow(2,1-d))*u>=2&&(s++,u/=2),s+d>=h?(l=0,s=h):s+d>=1?(l=(e*u-1)*Math.pow(2,n),s+=d):(l=e*Math.pow(2,d-1)*Math.pow(2,n),s=0));n>=8;t[r+y]=255&l,y+=w,l/=256,n-=8);for(s=s<<n|l,c+=n;c>0;t[r+y]=255&s,y+=w,s/=256,c-=8);t[r+y-w]|=128*S}},St={},Us=_c,kr=Qb,cc=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;St.Buffer=O,St.SlowBuffer=function(t){return+t!=t&&(t=0),O.alloc(+t)},St.INSPECT_MAX_BYTES=50;St.kMaxLength=2147483647,O.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),t.foo()===42}catch{return!1}}(),O.TYPED_ARRAY_SUPPORT||typeof console>"u"||typeof console.error!="function"||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(O.prototype,"parent",{enumerable:!0,get:function(){if(O.isBuffer(this))return this.buffer}}),Object.defineProperty(O.prototype,"offset",{enumerable:!0,get:function(){if(O.isBuffer(this))return this.byteOffset}}),O.poolSize=8192,O.from=function(t,e,r){return mc(t,e,r)},Object.setPrototypeOf(O.prototype,Uint8Array.prototype),Object.setPrototypeOf(O,Uint8Array),O.alloc=function(t,e,r){return function(i,n,o){return Ec(i),i<=0?Et(i):n!==void 0?typeof o=="string"?Et(i).fill(n,o):Et(i).fill(n):Et(i)}(t,e,r)},O.allocUnsafe=function(t){return Ms(t)},O.allocUnsafeSlow=function(t){return Ms(t)},O.isBuffer=function(t){return t!=null&&t._isBuffer===!0&&t!==O.prototype},O.compare=function(t,e){if(vt(t,Uint8Array)&&(t=O.from(t,t.offset,t.byteLength)),vt(e,Uint8Array)&&(e=O.from(e,e.offset,e.byteLength)),!O.isBuffer(t)||!O.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,i=e.length,n=0,o=Math.min(r,i);n<o;++n)if(t[n]!==e[n]){r=t[n],i=e[n];break}return r<i?-1:i<r?1:0},O.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},O.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(t.length===0)return O.alloc(0);var r;if(e===void 0)for(e=0,r=0;r<t.length;++r)e+=t[r].length;var i=O.allocUnsafe(e),n=0;for(r=0;r<t.length;++r){var o=t[r];if(vt(o,Uint8Array)&&(o=O.from(o)),!O.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(i,n),n+=o.length}return i},O.byteLength=vc,O.prototype._isBuffer=!0,O.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)rr(this,e,e+1);return this},O.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)rr(this,e,e+3),rr(this,e+1,e+2);return this},O.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)rr(this,e,e+7),rr(this,e+1,e+6),rr(this,e+2,e+5),rr(this,e+3,e+4);return this},O.prototype.toString=function(){var t=this.length;return t===0?"":arguments.length===0?Ac(this,0,t):Gb.apply(this,arguments)},O.prototype.toLocaleString=O.prototype.toString,O.prototype.equals=function(t){if(!O.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||O.compare(this,t)===0},O.prototype.inspect=function(){var t="",e=St.INSPECT_MAX_BYTES;return t=this.toString("hex",0,e).replace(/(.{2})/g,"$1 ").trim(),this.length>e&&(t+=" ... "),"<Buffer "+t+">"},cc&&(O.prototype[cc]=O.prototype.inspect),O.prototype.compare=function(t,e,r,i,n){if(vt(t,Uint8Array)&&(t=O.from(t,t.offset,t.byteLength)),!O.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(e===void 0&&(e=0),r===void 0&&(r=t?t.length:0),i===void 0&&(i=0),n===void 0&&(n=this.length),e<0||r>t.length||i<0||n>this.length)throw new RangeError("out of range index");if(i>=n&&e>=r)return 0;if(i>=n)return-1;if(e>=r)return 1;if(this===t)return 0;for(var o=(n>>>=0)-(i>>>=0),s=(r>>>=0)-(e>>>=0),l=Math.min(o,s),u=this.slice(i,n),c=t.slice(e,r),h=0;h<l;++h)if(u[h]!==c[h]){o=u[h],s=c[h];break}return o<s?-1:s<o?1:0},O.prototype.includes=function(t,e,r){return this.indexOf(t,e,r)!==-1},O.prototype.indexOf=function(t,e,r){return dc(this,t,e,r,!0)},O.prototype.lastIndexOf=function(t,e,r){return dc(this,t,e,r,!1)},O.prototype.write=function(t,e,r,i){if(e===void 0)i="utf8",r=this.length,e=0;else if(r===void 0&&typeof e=="string")i=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e>>>=0,isFinite(r)?(r>>>=0,i===void 0&&(i="utf8")):(i=r,r=void 0)}var n=this.length-e;if((r===void 0||r>n)&&(r=n),t.length>0&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");i||(i="utf8");for(var o=!1;;)switch(i){case"hex":return Yb(this,t,e,r);case"utf8":case"utf-8":return Jb(this,t,e,r);case"ascii":return Sc(this,t,e,r);case"latin1":case"binary":return Xb(this,t,e,r);case"base64":return Zb(this,t,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return ew(this,t,e,r);default:if(o)throw new TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),o=!0}},O.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};O.prototype.slice=function(t,e){var r=this.length;(t=~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),(e=e===void 0?r:~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var i=this.subarray(t,e);return Object.setPrototypeOf(i,O.prototype),i},O.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||pe(t,e,this.length);for(var i=this[t],n=1,o=0;++o<e&&(n*=256);)i+=this[t+o]*n;return i},O.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||pe(t,e,this.length);for(var i=this[t+--e],n=1;e>0&&(n*=256);)i+=this[t+--e]*n;return i},O.prototype.readUInt8=function(t,e){return t>>>=0,e||pe(t,1,this.length),this[t]},O.prototype.readUInt16LE=function(t,e){return t>>>=0,e||pe(t,2,this.length),this[t]|this[t+1]<<8},O.prototype.readUInt16BE=function(t,e){return t>>>=0,e||pe(t,2,this.length),this[t]<<8|this[t+1]},O.prototype.readUInt32LE=function(t,e){return t>>>=0,e||pe(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},O.prototype.readUInt32BE=function(t,e){return t>>>=0,e||pe(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},O.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||pe(t,e,this.length);for(var i=this[t],n=1,o=0;++o<e&&(n*=256);)i+=this[t+o]*n;return i>=(n*=128)&&(i-=Math.pow(2,8*e)),i},O.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||pe(t,e,this.length);for(var i=e,n=1,o=this[t+--i];i>0&&(n*=256);)o+=this[t+--i]*n;return o>=(n*=128)&&(o-=Math.pow(2,8*e)),o},O.prototype.readInt8=function(t,e){return t>>>=0,e||pe(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},O.prototype.readInt16LE=function(t,e){t>>>=0,e||pe(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},O.prototype.readInt16BE=function(t,e){t>>>=0,e||pe(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},O.prototype.readInt32LE=function(t,e){return t>>>=0,e||pe(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},O.prototype.readInt32BE=function(t,e){return t>>>=0,e||pe(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},O.prototype.readFloatLE=function(t,e){return t>>>=0,e||pe(t,4,this.length),kr.read(this,t,!0,23,4)},O.prototype.readFloatBE=function(t,e){return t>>>=0,e||pe(t,4,this.length),kr.read(this,t,!1,23,4)},O.prototype.readDoubleLE=function(t,e){return t>>>=0,e||pe(t,8,this.length),kr.read(this,t,!0,52,8)},O.prototype.readDoubleBE=function(t,e){return t>>>=0,e||pe(t,8,this.length),kr.read(this,t,!1,52,8)},O.prototype.writeUIntLE=function(t,e,r,i){t=+t,e>>>=0,r>>>=0,i||Pe(this,t,e,r,Math.pow(2,8*r)-1,0);var n=1,o=0;for(this[e]=255&t;++o<r&&(n*=256);)this[e+o]=t/n&255;return e+r},O.prototype.writeUIntBE=function(t,e,r,i){t=+t,e>>>=0,r>>>=0,i||Pe(this,t,e,r,Math.pow(2,8*r)-1,0);var n=r-1,o=1;for(this[e+n]=255&t;--n>=0&&(o*=256);)this[e+n]=t/o&255;return e+r},O.prototype.writeUInt8=function(t,e,r){return t=+t,e>>>=0,r||Pe(this,t,e,1,255,0),this[e]=255&t,e+1},O.prototype.writeUInt16LE=function(t,e,r){return t=+t,e>>>=0,r||Pe(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},O.prototype.writeUInt16BE=function(t,e,r){return t=+t,e>>>=0,r||Pe(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},O.prototype.writeUInt32LE=function(t,e,r){return t=+t,e>>>=0,r||Pe(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},O.prototype.writeUInt32BE=function(t,e,r){return t=+t,e>>>=0,r||Pe(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},O.prototype.writeIntLE=function(t,e,r,i){if(t=+t,e>>>=0,!i){var n=Math.pow(2,8*r-1);Pe(this,t,e,r,n-1,-n)}var o=0,s=1,l=0;for(this[e]=255&t;++o<r&&(s*=256);)t<0&&l===0&&this[e+o-1]!==0&&(l=1),this[e+o]=(t/s>>0)-l&255;return e+r},O.prototype.writeIntBE=function(t,e,r,i){if(t=+t,e>>>=0,!i){var n=Math.pow(2,8*r-1);Pe(this,t,e,r,n-1,-n)}var o=r-1,s=1,l=0;for(this[e+o]=255&t;--o>=0&&(s*=256);)t<0&&l===0&&this[e+o+1]!==0&&(l=1),this[e+o]=(t/s>>0)-l&255;return e+r},O.prototype.writeInt8=function(t,e,r){return t=+t,e>>>=0,r||Pe(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},O.prototype.writeInt16LE=function(t,e,r){return t=+t,e>>>=0,r||Pe(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},O.prototype.writeInt16BE=function(t,e,r){return t=+t,e>>>=0,r||Pe(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},O.prototype.writeInt32LE=function(t,e,r){return t=+t,e>>>=0,r||Pe(this,t,e,4,2147483647,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},O.prototype.writeInt32BE=function(t,e,r){return t=+t,e>>>=0,r||Pe(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},O.prototype.writeFloatLE=function(t,e,r){return gc(this,t,e,!0,r)},O.prototype.writeFloatBE=function(t,e,r){return gc(this,t,e,!1,r)},O.prototype.writeDoubleLE=function(t,e,r){return yc(this,t,e,!0,r)},O.prototype.writeDoubleBE=function(t,e,r){return yc(this,t,e,!1,r)},O.prototype.copy=function(t,e,r,i){if(!O.isBuffer(t))throw new TypeError("argument should be a Buffer");if(r||(r=0),i||i===0||(i=this.length),e>=t.length&&(e=t.length),e||(e=0),i>0&&i<r&&(i=r),i===r||t.length===0||this.length===0)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(i<0)throw new RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),t.length-e<i-r&&(i=t.length-e+r);var n=i-r;if(this===t&&typeof Uint8Array.prototype.copyWithin=="function")this.copyWithin(e,r,i);else if(this===t&&r<e&&e<i)for(var o=n-1;o>=0;--o)t[o+e]=this[o+r];else Uint8Array.prototype.set.call(t,this.subarray(r,i),e);return n},O.prototype.fill=function(t,e,r,i){if(typeof t=="string"){if(typeof e=="string"?(i=e,e=0,r=this.length):typeof r=="string"&&(i=r,r=this.length),i!==void 0&&typeof i!="string")throw new TypeError("encoding must be a string");if(typeof i=="string"&&!O.isEncoding(i))throw new TypeError("Unknown encoding: "+i);if(t.length===1){var n=t.charCodeAt(0);(i==="utf8"&&n<128||i==="latin1")&&(t=n)}}else typeof t=="number"?t&=255:typeof t=="boolean"&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;var o;if(e>>>=0,r=r===void 0?this.length:r>>>0,t||(t=0),typeof t=="number")for(o=e;o<r;++o)this[o]=t;else{var s=O.isBuffer(t)?t:O.from(t,i),l=s.length;if(l===0)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(o=0;o<r-e;++o)this[o+e]=s[o%l]}return this};ow=/[^+/0-9A-Za-z-_]/g;lw=function(){for(var t=new Array(256),e=0;e<16;++e)for(var r=16*e,i=0;i<16;++i)t[r+i]="0123456789abcdef"[e]+"0123456789abcdef"[i];return t}();St.Buffer;St.INSPECT_MAX_BYTES;St.kMaxLength;tn={},rn=St,it=rn.Buffer;it.from&&it.alloc&&it.allocUnsafe&&it.allocUnsafeSlow?tn=rn:(bc(rn,tn),tn.Buffer=ir),ir.prototype=Object.create(it.prototype),bc(it,ir),ir.from=function(t,e,r){if(typeof t=="number")throw new TypeError("Argument must not be a number");return it(t,e,r)},ir.alloc=function(t,e,r){if(typeof t!="number")throw new TypeError("Argument must be a number");var i=it(t);return e!==void 0?typeof r=="string"?i.fill(e,r):i.fill(e):i.fill(0),i},ir.allocUnsafe=function(t){if(typeof t!="number")throw new TypeError("Argument must be a number");return it(t)},ir.allocUnsafeSlow=function(t){if(typeof t!="number")throw new TypeError("Argument must be a number");return rn.SlowBuffer(t)};aw=tn,nr={},qs=aw.Buffer,wc=qs.isEncoding||function(t){switch((t=""+t)&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};nr.StringDecoder=ui,ui.prototype.write=function(t){if(t.length===0)return"";var e,r;if(this.lastNeed){if((e=this.fillLast(t))===void 0)return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<t.length?e?e+this.text(t,r):this.text(t,r):e||""},ui.prototype.end=function(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+"\uFFFD":e},ui.prototype.text=function(t,e){var r=function(n,o,s){var l=o.length-1;if(l<s)return 0;var u=Ls(o[l]);return u>=0?(u>0&&(n.lastNeed=u-1),u):--l<s||u===-2?0:(u=Ls(o[l]))>=0?(u>0&&(n.lastNeed=u-2),u):--l<s||u===-2?0:(u=Ls(o[l]))>=0?(u>0&&(u===2?u=0:n.lastNeed=u-3),u):0}(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=r;var i=t.length-(r-this.lastNeed);return t.copy(this.lastChar,0,i),t.toString("utf8",e,i)},ui.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length};nr.StringDecoder;nr.StringDecoder});var Rc={};zt(Rc,{StringDecoder:()=>yw,default:()=>nr});var yw,Cc=ge(()=>{_();E();m();Fs();Fs();yw=nr.StringDecoder});var Ws=L((fT,Oc)=>{"use strict";_();E();m();var Bc=Ut(),{PromisePrototypeThen:bw,SymbolAsyncIterator:Pc,SymbolIterator:xc}=ce(),{Buffer:ww}=(ye(),Z(me)),{ERR_INVALID_ARG_TYPE:_w,ERR_STREAM_NULL_VALUES:mw}=Se().codes;function Ew(t,e,r){let i;if(typeof e=="string"||e instanceof ww)return new t({objectMode:!0,...r,read(){this.push(e),this.push(null)}});let n;if(e&&e[Pc])n=!0,i=e[Pc]();else if(e&&e[xc])n=!1,i=e[xc]();else throw new _w("iterable",["Iterable"],e);let o=new t({objectMode:!0,highWaterMark:1,...r}),s=!1;o._read=function(){s||(s=!0,u())},o._destroy=function(c,h){bw(l(c),()=>Bc.nextTick(h,c),d=>Bc.nextTick(h,d||c))};async function l(c){let h=c!=null,d=typeof i.throw=="function";if(h&&d){let{value:g,done:y}=await i.throw(c);if(await g,y)return}if(typeof i.return=="function"){let{value:g}=await i.return();await g}}async function u(){for(;;){try{let{value:c,done:h}=n?await i.next():i.next();if(h)o.push(null);else{let d=c&&typeof c.then=="function"?await c:c;if(d===null)throw s=!1,new mw;if(o.push(d))continue;s=!1}}catch(c){o.destroy(c)}break}}return o}Oc.exports=Ew});var fi=L((bT,Vc)=>{_();E();m();var He=Ut(),{ArrayPrototypeIndexOf:vw,NumberIsInteger:Sw,NumberIsNaN:Aw,NumberParseInt:Iw,ObjectDefineProperties:Uc,ObjectKeys:Tw,ObjectSetPrototypeOf:Mc,Promise:Rw,SafeSet:Cw,SymbolAsyncIterator:Bw,Symbol:Pw}=ce();Vc.exports=F;F.ReadableState=Qs;var{EventEmitter:xw}=(er(),Z(Zt)),{Stream:Nt,prependListener:Ow}=Yi(),{Buffer:$s}=(ye(),Z(me)),{addAbortSignal:kw}=ai(),Lw=mt(),$=Je().debuglog("stream",t=>{$=t}),Uw=sc(),Ur=Xt(),{getHighWaterMark:Mw,getDefaultHighWaterMark:Nw}=Zi(),{aggregateTwoErrors:kc,codes:{ERR_INVALID_ARG_TYPE:qw,ERR_METHOD_NOT_IMPLEMENTED:Dw,ERR_OUT_OF_RANGE:jw,ERR_STREAM_PUSH_AFTER_EOF:Fw,ERR_STREAM_UNSHIFT_AFTER_END_EVENT:Ww}}=Se(),{validateObject:$w}=li(),sr=Pw("kPaused"),{StringDecoder:Nc}=(Cc(),Z(Rc)),Hw=Ws();Mc(F.prototype,Nt.prototype);Mc(F,Nt);var Hs=()=>{},{errorOrDestroy:Lr}=Ur;function Qs(t,e,r){typeof r!="boolean"&&(r=e instanceof nt()),this.objectMode=!!(t&&t.objectMode),r&&(this.objectMode=this.objectMode||!!(t&&t.readableObjectMode)),this.highWaterMark=t?Mw(this,t,"readableHighWaterMark",r):Nw(!1),this.buffer=new Uw,this.length=0,this.pipes=[],this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.constructed=!0,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this[sr]=null,this.errorEmitted=!1,this.emitClose=!t||t.emitClose!==!1,this.autoDestroy=!t||t.autoDestroy!==!1,this.destroyed=!1,this.errored=null,this.closed=!1,this.closeEmitted=!1,this.defaultEncoding=t&&t.defaultEncoding||"utf8",this.awaitDrainWriters=null,this.multiAwaitDrain=!1,this.readingMore=!1,this.dataEmitted=!1,this.decoder=null,this.encoding=null,t&&t.encoding&&(this.decoder=new Nc(t.encoding),this.encoding=t.encoding)}function F(t){if(!(this instanceof F))return new F(t);let e=this instanceof nt();this._readableState=new Qs(t,this,e),t&&(typeof t.read=="function"&&(this._read=t.read),typeof t.destroy=="function"&&(this._destroy=t.destroy),typeof t.construct=="function"&&(this._construct=t.construct),t.signal&&!e&&kw(t.signal,this)),Nt.call(this,t),Ur.construct(this,()=>{this._readableState.needReadable&&sn(this,this._readableState)})}F.prototype.destroy=Ur.destroy;F.prototype._undestroy=Ur.undestroy;F.prototype._destroy=function(t,e){e(t)};F.prototype[xw.captureRejectionSymbol]=function(t){this.destroy(t)};F.prototype.push=function(t,e){return qc(this,t,e,!1)};F.prototype.unshift=function(t,e){return qc(this,t,e,!0)};function qc(t,e,r,i){$("readableAddChunk",e);let n=t._readableState,o;if(n.objectMode||(typeof e=="string"?(r=r||n.defaultEncoding,n.encoding!==r&&(i&&n.encoding?e=$s.from(e,r).toString(n.encoding):(e=$s.from(e,r),r=""))):e instanceof $s?r="":Nt._isUint8Array(e)?(e=Nt._uint8ArrayToBuffer(e),r=""):e!=null&&(o=new qw("chunk",["string","Buffer","Uint8Array"],e))),o)Lr(t,o);else if(e===null)n.reading=!1,Kw(t,n);else if(n.objectMode||e&&e.length>0)if(i)if(n.endEmitted)Lr(t,new Ww);else{if(n.destroyed||n.errored)return!1;Vs(t,n,e,!0)}else if(n.ended)Lr(t,new Fw);else{if(n.destroyed||n.errored)return!1;n.reading=!1,n.decoder&&!r?(e=n.decoder.write(e),n.objectMode||e.length!==0?Vs(t,n,e,!1):sn(t,n)):Vs(t,n,e,!1)}else i||(n.reading=!1,sn(t,n));return!n.ended&&(n.length<n.highWaterMark||n.length===0)}function Vs(t,e,r,i){e.flowing&&e.length===0&&!e.sync&&t.listenerCount("data")>0?(e.multiAwaitDrain?e.awaitDrainWriters.clear():e.awaitDrainWriters=null,e.dataEmitted=!0,t.emit("data",r)):(e.length+=e.objectMode?1:r.length,i?e.buffer.unshift(r):e.buffer.push(r),e.needReadable&&on(t)),sn(t,e)}F.prototype.isPaused=function(){let t=this._readableState;return t[sr]===!0||t.flowing===!1};F.prototype.setEncoding=function(t){let e=new Nc(t);this._readableState.decoder=e,this._readableState.encoding=this._readableState.decoder.encoding;let r=this._readableState.buffer,i="";for(let n of r)i+=e.write(n);return r.clear(),i!==""&&r.push(i),this._readableState.length=i.length,this};var Vw=1073741824;function zw(t){if(t>Vw)throw new jw("size","<= 1GiB",t);return t--,t|=t>>>1,t|=t>>>2,t|=t>>>4,t|=t>>>8,t|=t>>>16,t++,t}function Lc(t,e){return t<=0||e.length===0&&e.ended?0:e.objectMode?1:Aw(t)?e.flowing&&e.length?e.buffer.first().length:e.length:t<=e.length?t:e.ended?e.length:0}F.prototype.read=function(t){$("read",t),t===void 0?t=NaN:Sw(t)||(t=Iw(t,10));let e=this._readableState,r=t;if(t>e.highWaterMark&&(e.highWaterMark=zw(t)),t!==0&&(e.emittedReadable=!1),t===0&&e.needReadable&&((e.highWaterMark!==0?e.length>=e.highWaterMark:e.length>0)||e.ended))return $("read: emitReadable",e.length,e.ended),e.length===0&&e.ended?zs(this):on(this),null;if(t=Lc(t,e),t===0&&e.ended)return e.length===0&&zs(this),null;let i=e.needReadable;if($("need readable",i),(e.length===0||e.length-t<e.highWaterMark)&&(i=!0,$("length less than watermark",i)),e.ended||e.reading||e.destroyed||e.errored||!e.constructed)i=!1,$("reading, ended or constructing",i);else if(i){$("do read"),e.reading=!0,e.sync=!0,e.length===0&&(e.needReadable=!0);try{this._read(e.highWaterMark)}catch(o){Lr(this,o)}e.sync=!1,e.reading||(t=Lc(r,e))}let n;return t>0?n=$c(t,e):n=null,n===null?(e.needReadable=e.length<=e.highWaterMark,t=0):(e.length-=t,e.multiAwaitDrain?e.awaitDrainWriters.clear():e.awaitDrainWriters=null),e.length===0&&(e.ended||(e.needReadable=!0),r!==t&&e.ended&&zs(this)),n!==null&&!e.errorEmitted&&!e.closeEmitted&&(e.dataEmitted=!0,this.emit("data",n)),n};function Kw(t,e){if($("onEofChunk"),!e.ended){if(e.decoder){let r=e.decoder.end();r&&r.length&&(e.buffer.push(r),e.length+=e.objectMode?1:r.length)}e.ended=!0,e.sync?on(t):(e.needReadable=!1,e.emittedReadable=!0,Dc(t))}}function on(t){let e=t._readableState;$("emitReadable",e.needReadable,e.emittedReadable),e.needReadable=!1,e.emittedReadable||($("emitReadable",e.flowing),e.emittedReadable=!0,He.nextTick(Dc,t))}function Dc(t){let e=t._readableState;$("emitReadable_",e.destroyed,e.length,e.ended),!e.destroyed&&!e.errored&&(e.length||e.ended)&&(t.emit("readable"),e.emittedReadable=!1),e.needReadable=!e.flowing&&!e.ended&&e.length<=e.highWaterMark,Fc(t)}function sn(t,e){!e.readingMore&&e.constructed&&(e.readingMore=!0,He.nextTick(Qw,t,e))}function Qw(t,e){for(;!e.reading&&!e.ended&&(e.length<e.highWaterMark||e.flowing&&e.length===0);){let r=e.length;if($("maybeReadMore read 0"),t.read(0),r===e.length)break}e.readingMore=!1}F.prototype._read=function(t){throw new Dw("_read()")};F.prototype.pipe=function(t,e){let r=this,i=this._readableState;i.pipes.length===1&&(i.multiAwaitDrain||(i.multiAwaitDrain=!0,i.awaitDrainWriters=new Cw(i.awaitDrainWriters?[i.awaitDrainWriters]:[]))),i.pipes.push(t),$("pipe count=%d opts=%j",i.pipes.length,e);let o=(!e||e.end!==!1)&&t!==He.stdout&&t!==He.stderr?l:A;i.endEmitted?He.nextTick(o):r.once("end",o),t.on("unpipe",s);function s(I,P){$("onunpipe"),I===r&&P&&P.hasUnpiped===!1&&(P.hasUnpiped=!0,h())}function l(){$("onend"),t.end()}let u,c=!1;function h(){$("cleanup"),t.removeListener("close",w),t.removeListener("finish",S),u&&t.removeListener("drain",u),t.removeListener("error",y),t.removeListener("unpipe",s),r.removeListener("end",l),r.removeListener("end",A),r.removeListener("data",g),c=!0,u&&i.awaitDrainWriters&&(!t._writableState||t._writableState.needDrain)&&u()}function d(){c||(i.pipes.length===1&&i.pipes[0]===t?($("false write response, pause",0),i.awaitDrainWriters=t,i.multiAwaitDrain=!1):i.pipes.length>1&&i.pipes.includes(t)&&($("false write response, pause",i.awaitDrainWriters.size),i.awaitDrainWriters.add(t)),r.pause()),u||(u=Gw(r,t),t.on("drain",u))}r.on("data",g);function g(I){$("ondata");let P=t.write(I);$("dest.write",P),P===!1&&d()}function y(I){if($("onerror",I),A(),t.removeListener("error",y),t.listenerCount("error")===0){let P=t._writableState||t._readableState;P&&!P.errorEmitted?Lr(t,I):t.emit("error",I)}}Ow(t,"error",y);function w(){t.removeListener("finish",S),A()}t.once("close",w);function S(){$("onfinish"),t.removeListener("close",w),A()}t.once("finish",S);function A(){$("unpipe"),r.unpipe(t)}return t.emit("pipe",r),t.writableNeedDrain===!0?i.flowing&&d():i.flowing||($("pipe resume"),r.resume()),t};function Gw(t,e){return function(){let i=t._readableState;i.awaitDrainWriters===e?($("pipeOnDrain",1),i.awaitDrainWriters=null):i.multiAwaitDrain&&($("pipeOnDrain",i.awaitDrainWriters.size),i.awaitDrainWriters.delete(e)),(!i.awaitDrainWriters||i.awaitDrainWriters.size===0)&&t.listenerCount("data")&&t.resume()}}F.prototype.unpipe=function(t){let e=this._readableState,r={hasUnpiped:!1};if(e.pipes.length===0)return this;if(!t){let n=e.pipes;e.pipes=[],this.pause();for(let o=0;o<n.length;o++)n[o].emit("unpipe",this,{hasUnpiped:!1});return this}let i=vw(e.pipes,t);return i===-1?this:(e.pipes.splice(i,1),e.pipes.length===0&&this.pause(),t.emit("unpipe",this,r),this)};F.prototype.on=function(t,e){let r=Nt.prototype.on.call(this,t,e),i=this._readableState;return t==="data"?(i.readableListening=this.listenerCount("readable")>0,i.flowing!==!1&&this.resume()):t==="readable"&&!i.endEmitted&&!i.readableListening&&(i.readableListening=i.needReadable=!0,i.flowing=!1,i.emittedReadable=!1,$("on readable",i.length,i.reading),i.length?on(this):i.reading||He.nextTick(Yw,this)),r};F.prototype.addListener=F.prototype.on;F.prototype.removeListener=function(t,e){let r=Nt.prototype.removeListener.call(this,t,e);return t==="readable"&&He.nextTick(jc,this),r};F.prototype.off=F.prototype.removeListener;F.prototype.removeAllListeners=function(t){let e=Nt.prototype.removeAllListeners.apply(this,arguments);return(t==="readable"||t===void 0)&&He.nextTick(jc,this),e};function jc(t){let e=t._readableState;e.readableListening=t.listenerCount("readable")>0,e.resumeScheduled&&e[sr]===!1?e.flowing=!0:t.listenerCount("data")>0?t.resume():e.readableListening||(e.flowing=null)}function Yw(t){$("readable nexttick read 0"),t.read(0)}F.prototype.resume=function(){let t=this._readableState;return t.flowing||($("resume"),t.flowing=!t.readableListening,Jw(this,t)),t[sr]=!1,this};function Jw(t,e){e.resumeScheduled||(e.resumeScheduled=!0,He.nextTick(Xw,t,e))}function Xw(t,e){$("resume",e.reading),e.reading||t.read(0),e.resumeScheduled=!1,t.emit("resume"),Fc(t),e.flowing&&!e.reading&&t.read(0)}F.prototype.pause=function(){return $("call pause flowing=%j",this._readableState.flowing),this._readableState.flowing!==!1&&($("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState[sr]=!0,this};function Fc(t){let e=t._readableState;for($("flow",e.flowing);e.flowing&&t.read()!==null;);}F.prototype.wrap=function(t){let e=!1;t.on("data",i=>{!this.push(i)&&t.pause&&(e=!0,t.pause())}),t.on("end",()=>{this.push(null)}),t.on("error",i=>{Lr(this,i)}),t.on("close",()=>{this.destroy()}),t.on("destroy",()=>{this.destroy()}),this._read=()=>{e&&t.resume&&(e=!1,t.resume())};let r=Tw(t);for(let i=1;i<r.length;i++){let n=r[i];this[n]===void 0&&typeof t[n]=="function"&&(this[n]=t[n].bind(t))}return this};F.prototype[Bw]=function(){return Wc(this)};F.prototype.iterator=function(t){return t!==void 0&&$w(t,"options"),Wc(this,t)};function Wc(t,e){typeof t.read!="function"&&(t=F.wrap(t,{objectMode:!0}));let r=Zw(t,e);return r.stream=t,r}async function*Zw(t,e){let r=Hs;function i(s){this===t?(r(),r=Hs):r=s}t.on("readable",i);let n,o=Lw(t,{writable:!1},s=>{n=s?kc(n,s):null,r(),r=Hs});try{for(;;){let s=t.destroyed?null:t.read();if(s!==null)yield s;else{if(n)throw n;if(n===null)return;await new Rw(i)}}}catch(s){throw n=kc(n,s),n}finally{(n||e?.destroyOnReturn!==!1)&&(n===void 0||t._readableState.autoDestroy)?Ur.destroyer(t,null):(t.off("readable",i),o())}}Uc(F.prototype,{readable:{__proto__:null,get(){let t=this._readableState;return!!t&&t.readable!==!1&&!t.destroyed&&!t.errorEmitted&&!t.endEmitted},set(t){this._readableState&&(this._readableState.readable=!!t)}},readableDidRead:{__proto__:null,enumerable:!1,get:function(){return this._readableState.dataEmitted}},readableAborted:{__proto__:null,enumerable:!1,get:function(){return!!(this._readableState.readable!==!1&&(this._readableState.destroyed||this._readableState.errored)&&!this._readableState.endEmitted)}},readableHighWaterMark:{__proto__:null,enumerable:!1,get:function(){return this._readableState.highWaterMark}},readableBuffer:{__proto__:null,enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}},readableFlowing:{__proto__:null,enumerable:!1,get:function(){return this._readableState.flowing},set:function(t){this._readableState&&(this._readableState.flowing=t)}},readableLength:{__proto__:null,enumerable:!1,get(){return this._readableState.length}},readableObjectMode:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.objectMode:!1}},readableEncoding:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.encoding:null}},errored:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.errored:null}},closed:{__proto__:null,get(){return this._readableState?this._readableState.closed:!1}},destroyed:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.destroyed:!1},set(t){this._readableState&&(this._readableState.destroyed=t)}},readableEnded:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.endEmitted:!1}}});Uc(Qs.prototype,{pipesCount:{__proto__:null,get(){return this.pipes.length}},paused:{__proto__:null,get(){return this[sr]!==!1},set(t){this[sr]=!!t}}});F._fromList=$c;function $c(t,e){if(e.length===0)return null;let r;return e.objectMode?r=e.buffer.shift():!t||t>=e.length?(e.decoder?r=e.buffer.join(""):e.buffer.length===1?r=e.buffer.first():r=e.buffer.concat(e.length),e.buffer.clear()):r=e.buffer.consume(t,e.decoder),r}function zs(t){let e=t._readableState;$("endReadable",e.endEmitted),e.endEmitted||(e.ended=!0,He.nextTick(e_,e,t))}function e_(t,e){if($("endReadableNT",t.endEmitted,t.length),!t.errored&&!t.closeEmitted&&!t.endEmitted&&t.length===0){if(t.endEmitted=!0,e.emit("end"),e.writable&&e.allowHalfOpen===!1)He.nextTick(t_,e);else if(t.autoDestroy){let r=e._writableState;(!r||r.autoDestroy&&(r.finished||r.writable===!1))&&e.destroy()}}}function t_(t){t.writable&&!t.writableEnded&&!t.destroyed&&t.end()}F.from=function(t,e){return Hw(F,t,e)};var Ks;function Hc(){return Ks===void 0&&(Ks={}),Ks}F.fromWeb=function(t,e){return Hc().newStreamReadableFromReadableStream(t,e)};F.toWeb=function(t,e){return Hc().newReadableStreamFromStreamReadable(t,e)};F.wrap=function(t,e){var r,i;return new F({objectMode:(r=(i=t.readableObjectMode)!==null&&i!==void 0?i:t.objectMode)!==null&&r!==void 0?r:!0,...e,destroy(n,o){Ur.destroyer(t,n),o(n)}}).wrap(t)}});var to=L((AT,ih)=>{_();E();m();var or=Ut(),{ArrayPrototypeSlice:Qc,Error:r_,FunctionPrototypeSymbolHasInstance:Gc,ObjectDefineProperty:Yc,ObjectDefineProperties:i_,ObjectSetPrototypeOf:Jc,StringPrototypeToLowerCase:n_,Symbol:s_,SymbolHasInstance:o_}=ce();ih.exports=ie;ie.WritableState=di;var{EventEmitter:l_}=(er(),Z(Zt)),ci=Yi().Stream,{Buffer:ln}=(ye(),Z(me)),fn=Xt(),{addAbortSignal:a_}=ai(),{getHighWaterMark:u_,getDefaultHighWaterMark:f_}=Zi(),{ERR_INVALID_ARG_TYPE:c_,ERR_METHOD_NOT_IMPLEMENTED:h_,ERR_MULTIPLE_CALLBACK:Xc,ERR_STREAM_CANNOT_PIPE:d_,ERR_STREAM_DESTROYED:hi,ERR_STREAM_ALREADY_FINISHED:p_,ERR_STREAM_NULL_VALUES:g_,ERR_STREAM_WRITE_AFTER_END:y_,ERR_UNKNOWN_ENCODING:Zc}=Se().codes,{errorOrDestroy:Mr}=fn;Jc(ie.prototype,ci.prototype);Jc(ie,ci);function Js(){}var Nr=s_("kOnFinished");function di(t,e,r){typeof r!="boolean"&&(r=e instanceof nt()),this.objectMode=!!(t&&t.objectMode),r&&(this.objectMode=this.objectMode||!!(t&&t.writableObjectMode)),this.highWaterMark=t?u_(this,t,"writableHighWaterMark",r):f_(!1),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;let i=!!(t&&t.decodeStrings===!1);this.decodeStrings=!i,this.defaultEncoding=t&&t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=w_.bind(void 0,e),this.writecb=null,this.writelen=0,this.afterWriteTickInfo=null,un(this),this.pendingcb=0,this.constructed=!0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!t||t.emitClose!==!1,this.autoDestroy=!t||t.autoDestroy!==!1,this.errored=null,this.closed=!1,this.closeEmitted=!1,this[Nr]=[]}function un(t){t.buffered=[],t.bufferedIndex=0,t.allBuffers=!0,t.allNoop=!0}di.prototype.getBuffer=function(){return Qc(this.buffered,this.bufferedIndex)};Yc(di.prototype,"bufferedRequestCount",{__proto__:null,get(){return this.buffered.length-this.bufferedIndex}});function ie(t){let e=this instanceof nt();if(!e&&!Gc(ie,this))return new ie(t);this._writableState=new di(t,this,e),t&&(typeof t.write=="function"&&(this._write=t.write),typeof t.writev=="function"&&(this._writev=t.writev),typeof t.destroy=="function"&&(this._destroy=t.destroy),typeof t.final=="function"&&(this._final=t.final),typeof t.construct=="function"&&(this._construct=t.construct),t.signal&&a_(t.signal,this)),ci.call(this,t),fn.construct(this,()=>{let r=this._writableState;r.writing||Zs(this,r),eo(this,r)})}Yc(ie,o_,{__proto__:null,value:function(t){return Gc(this,t)?!0:this!==ie?!1:t&&t._writableState instanceof di}});ie.prototype.pipe=function(){Mr(this,new d_)};function eh(t,e,r,i){let n=t._writableState;if(typeof r=="function")i=r,r=n.defaultEncoding;else{if(!r)r=n.defaultEncoding;else if(r!=="buffer"&&!ln.isEncoding(r))throw new Zc(r);typeof i!="function"&&(i=Js)}if(e===null)throw new g_;if(!n.objectMode)if(typeof e=="string")n.decodeStrings!==!1&&(e=ln.from(e,r),r="buffer");else if(e instanceof ln)r="buffer";else if(ci._isUint8Array(e))e=ci._uint8ArrayToBuffer(e),r="buffer";else throw new c_("chunk",["string","Buffer","Uint8Array"],e);let o;return n.ending?o=new y_:n.destroyed&&(o=new hi("write")),o?(or.nextTick(i,o),Mr(t,o,!0),o):(n.pendingcb++,b_(t,n,e,r,i))}ie.prototype.write=function(t,e,r){return eh(this,t,e,r)===!0};ie.prototype.cork=function(){this._writableState.corked++};ie.prototype.uncork=function(){let t=this._writableState;t.corked&&(t.corked--,t.writing||Zs(this,t))};ie.prototype.setDefaultEncoding=function(e){if(typeof e=="string"&&(e=n_(e)),!ln.isEncoding(e))throw new Zc(e);return this._writableState.defaultEncoding=e,this};function b_(t,e,r,i,n){let o=e.objectMode?1:r.length;e.length+=o;let s=e.length<e.highWaterMark;return s||(e.needDrain=!0),e.writing||e.corked||e.errored||!e.constructed?(e.buffered.push({chunk:r,encoding:i,callback:n}),e.allBuffers&&i!=="buffer"&&(e.allBuffers=!1),e.allNoop&&n!==Js&&(e.allNoop=!1)):(e.writelen=o,e.writecb=n,e.writing=!0,e.sync=!0,t._write(r,i,e.onwrite),e.sync=!1),s&&!e.errored&&!e.destroyed}function zc(t,e,r,i,n,o,s){e.writelen=i,e.writecb=s,e.writing=!0,e.sync=!0,e.destroyed?e.onwrite(new hi("write")):r?t._writev(n,e.onwrite):t._write(n,o,e.onwrite),e.sync=!1}function Kc(t,e,r,i){--e.pendingcb,i(r),Xs(e),Mr(t,r)}function w_(t,e){let r=t._writableState,i=r.sync,n=r.writecb;if(typeof n!="function"){Mr(t,new Xc);return}r.writing=!1,r.writecb=null,r.length-=r.writelen,r.writelen=0,e?(e.stack,r.errored||(r.errored=e),t._readableState&&!t._readableState.errored&&(t._readableState.errored=e),i?or.nextTick(Kc,t,r,e,n):Kc(t,r,e,n)):(r.buffered.length>r.bufferedIndex&&Zs(t,r),i?r.afterWriteTickInfo!==null&&r.afterWriteTickInfo.cb===n?r.afterWriteTickInfo.count++:(r.afterWriteTickInfo={count:1,cb:n,stream:t,state:r},or.nextTick(__,r.afterWriteTickInfo)):th(t,r,1,n))}function __({stream:t,state:e,count:r,cb:i}){return e.afterWriteTickInfo=null,th(t,e,r,i)}function th(t,e,r,i){for(!e.ending&&!t.destroyed&&e.length===0&&e.needDrain&&(e.needDrain=!1,t.emit("drain"));r-- >0;)e.pendingcb--,i();e.destroyed&&Xs(e),eo(t,e)}function Xs(t){if(t.writing)return;for(let n=t.bufferedIndex;n<t.buffered.length;++n){var e;let{chunk:o,callback:s}=t.buffered[n],l=t.objectMode?1:o.length;t.length-=l,s((e=t.errored)!==null&&e!==void 0?e:new hi("write"))}let r=t[Nr].splice(0);for(let n=0;n<r.length;n++){var i;r[n]((i=t.errored)!==null&&i!==void 0?i:new hi("end"))}un(t)}function Zs(t,e){if(e.corked||e.bufferProcessing||e.destroyed||!e.constructed)return;let{buffered:r,bufferedIndex:i,objectMode:n}=e,o=r.length-i;if(!o)return;let s=i;if(e.bufferProcessing=!0,o>1&&t._writev){e.pendingcb-=o-1;let l=e.allNoop?Js:c=>{for(let h=s;h<r.length;++h)r[h].callback(c)},u=e.allNoop&&s===0?r:Qc(r,s);u.allBuffers=e.allBuffers,zc(t,e,!0,e.length,u,"",l),un(e)}else{do{let{chunk:l,encoding:u,callback:c}=r[s];r[s++]=null;let h=n?1:l.length;zc(t,e,!1,h,l,u,c)}while(s<r.length&&!e.writing);s===r.length?un(e):s>256?(r.splice(0,s),e.bufferedIndex=0):e.bufferedIndex=s}e.bufferProcessing=!1}ie.prototype._write=function(t,e,r){if(this._writev)this._writev([{chunk:t,encoding:e}],r);else throw new h_("_write()")};ie.prototype._writev=null;ie.prototype.end=function(t,e,r){let i=this._writableState;typeof t=="function"?(r=t,t=null,e=null):typeof e=="function"&&(r=e,e=null);let n;if(t!=null){let o=eh(this,t,e);o instanceof r_&&(n=o)}return i.corked&&(i.corked=1,this.uncork()),n||(!i.errored&&!i.ending?(i.ending=!0,eo(this,i,!0),i.ended=!0):i.finished?n=new p_("end"):i.destroyed&&(n=new hi("end"))),typeof r=="function"&&(n||i.finished?or.nextTick(r,n):i[Nr].push(r)),this};function an(t){return t.ending&&!t.destroyed&&t.constructed&&t.length===0&&!t.errored&&t.buffered.length===0&&!t.finished&&!t.writing&&!t.errorEmitted&&!t.closeEmitted}function m_(t,e){let r=!1;function i(n){if(r){Mr(t,n??Xc());return}if(r=!0,e.pendingcb--,n){let o=e[Nr].splice(0);for(let s=0;s<o.length;s++)o[s](n);Mr(t,n,e.sync)}else an(e)&&(e.prefinished=!0,t.emit("prefinish"),e.pendingcb++,or.nextTick(Ys,t,e))}e.sync=!0,e.pendingcb++;try{t._final(i)}catch(n){i(n)}e.sync=!1}function E_(t,e){!e.prefinished&&!e.finalCalled&&(typeof t._final=="function"&&!e.destroyed?(e.finalCalled=!0,m_(t,e)):(e.prefinished=!0,t.emit("prefinish")))}function eo(t,e,r){an(e)&&(E_(t,e),e.pendingcb===0&&(r?(e.pendingcb++,or.nextTick((i,n)=>{an(n)?Ys(i,n):n.pendingcb--},t,e)):an(e)&&(e.pendingcb++,Ys(t,e))))}function Ys(t,e){e.pendingcb--,e.finished=!0;let r=e[Nr].splice(0);for(let i=0;i<r.length;i++)r[i]();if(t.emit("finish"),e.autoDestroy){let i=t._readableState;(!i||i.autoDestroy&&(i.endEmitted||i.readable===!1))&&t.destroy()}}i_(ie.prototype,{closed:{__proto__:null,get(){return this._writableState?this._writableState.closed:!1}},destroyed:{__proto__:null,get(){return this._writableState?this._writableState.destroyed:!1},set(t){this._writableState&&(this._writableState.destroyed=t)}},writable:{__proto__:null,get(){let t=this._writableState;return!!t&&t.writable!==!1&&!t.destroyed&&!t.errored&&!t.ending&&!t.ended},set(t){this._writableState&&(this._writableState.writable=!!t)}},writableFinished:{__proto__:null,get(){return this._writableState?this._writableState.finished:!1}},writableObjectMode:{__proto__:null,get(){return this._writableState?this._writableState.objectMode:!1}},writableBuffer:{__proto__:null,get(){return this._writableState&&this._writableState.getBuffer()}},writableEnded:{__proto__:null,get(){return this._writableState?this._writableState.ending:!1}},writableNeedDrain:{__proto__:null,get(){let t=this._writableState;return t?!t.destroyed&&!t.ending&&t.needDrain:!1}},writableHighWaterMark:{__proto__:null,get(){return this._writableState&&this._writableState.highWaterMark}},writableCorked:{__proto__:null,get(){return this._writableState?this._writableState.corked:0}},writableLength:{__proto__:null,get(){return this._writableState&&this._writableState.length}},errored:{__proto__:null,enumerable:!1,get(){return this._writableState?this._writableState.errored:null}},writableAborted:{__proto__:null,enumerable:!1,get:function(){return!!(this._writableState.writable!==!1&&(this._writableState.destroyed||this._writableState.errored)&&!this._writableState.finished)}}});var v_=fn.destroy;ie.prototype.destroy=function(t,e){let r=this._writableState;return!r.destroyed&&(r.bufferedIndex<r.buffered.length||r[Nr].length)&&or.nextTick(Xs,r),v_.call(this,t,e),this};ie.prototype._undestroy=fn.undestroy;ie.prototype._destroy=function(t,e){e(t)};ie.prototype[l_.captureRejectionSymbol]=function(t){this.destroy(t)};var Gs;function rh(){return Gs===void 0&&(Gs={}),Gs}ie.fromWeb=function(t,e){return rh().newStreamWritableFromWritableStream(t,e)};ie.toWeb=function(t){return rh().newWritableStreamFromStreamWritable(t)}});var gh=L((xT,ph)=>{_();E();m();var ro=Ut(),S_=(ye(),Z(me)),{isReadable:A_,isWritable:I_,isIterable:nh,isNodeStream:T_,isReadableNodeStream:sh,isWritableNodeStream:oh,isDuplexNodeStream:R_}=tt(),lh=mt(),{AbortError:dh,codes:{ERR_INVALID_ARG_TYPE:C_,ERR_INVALID_RETURN_VALUE:ah}}=Se(),{destroyer:qr}=Xt(),B_=nt(),P_=fi(),{createDeferredPromise:uh}=Je(),fh=Ws(),ch=globalThis.Blob||S_.Blob,x_=typeof ch<"u"?function(e){return e instanceof ch}:function(e){return!1},O_=globalThis.AbortController||Di().AbortController,{FunctionPrototypeCall:hh}=ce(),lr=class extends B_{constructor(e){super(e),e?.readable===!1&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),e?.writable===!1&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)}};ph.exports=function t(e,r){if(R_(e))return e;if(sh(e))return cn({readable:e});if(oh(e))return cn({writable:e});if(T_(e))return cn({writable:!1,readable:!1});if(typeof e=="function"){let{value:n,write:o,final:s,destroy:l}=k_(e);if(nh(n))return fh(lr,n,{objectMode:!0,write:o,final:s,destroy:l});let u=n?.then;if(typeof u=="function"){let c,h=hh(u,n,d=>{if(d!=null)throw new ah("nully","body",d)},d=>{qr(c,d)});return c=new lr({objectMode:!0,readable:!1,write:o,final(d){s(async()=>{try{await h,ro.nextTick(d,null)}catch(g){ro.nextTick(d,g)}})},destroy:l})}throw new ah("Iterable, AsyncIterable or AsyncFunction",r,n)}if(x_(e))return t(e.arrayBuffer());if(nh(e))return fh(lr,e,{objectMode:!0,writable:!1});if(typeof e?.writable=="object"||typeof e?.readable=="object"){let n=e!=null&&e.readable?sh(e?.readable)?e?.readable:t(e.readable):void 0,o=e!=null&&e.writable?oh(e?.writable)?e?.writable:t(e.writable):void 0;return cn({readable:n,writable:o})}let i=e?.then;if(typeof i=="function"){let n;return hh(i,e,o=>{o!=null&&n.push(o),n.push(null)},o=>{qr(n,o)}),n=new lr({objectMode:!0,writable:!1,read(){}})}throw new C_(r,["Blob","ReadableStream","WritableStream","Stream","Iterable","AsyncIterable","Function","{ readable, writable } pair","Promise"],e)};function k_(t){let{promise:e,resolve:r}=uh(),i=new O_,n=i.signal;return{value:t(async function*(){for(;;){let s=e;e=null;let{chunk:l,done:u,cb:c}=await s;if(ro.nextTick(c),u)return;if(n.aborted)throw new dh(void 0,{cause:n.reason});({promise:e,resolve:r}=uh()),yield l}}(),{signal:n}),write(s,l,u){let c=r;r=null,c({chunk:s,done:!1,cb:u})},final(s){let l=r;r=null,l({done:!0,cb:s})},destroy(s,l){i.abort(),l(s)}}}function cn(t){let e=t.readable&&typeof t.readable.read!="function"?P_.wrap(t.readable):t.readable,r=t.writable,i=!!A_(e),n=!!I_(r),o,s,l,u,c;function h(d){let g=u;u=null,g?g(d):d&&c.destroy(d)}return c=new lr({readableObjectMode:!!(e!=null&&e.readableObjectMode),writableObjectMode:!!(r!=null&&r.writableObjectMode),readable:i,writable:n}),n&&(lh(r,d=>{n=!1,d&&qr(e,d),h(d)}),c._write=function(d,g,y){r.write(d,g)?y():o=y},c._final=function(d){r.end(),s=d},r.on("drain",function(){if(o){let d=o;o=null,d()}}),r.on("finish",function(){if(s){let d=s;s=null,d()}})),i&&(lh(e,d=>{i=!1,d&&qr(e,d),h(d)}),e.on("readable",function(){if(l){let d=l;l=null,d()}}),e.on("end",function(){c.push(null)}),c._read=function(){for(;;){let d=e.read();if(d===null){l=c._read;return}if(!c.push(d))return}}),c._destroy=function(d,g){!d&&u!==null&&(d=new dh),l=null,o=null,s=null,u===null?g(d):(u=g,qr(r,d),qr(e,d))},c}});var nt=L((qT,wh)=>{"use strict";_();E();m();var{ObjectDefineProperties:L_,ObjectGetOwnPropertyDescriptor:At,ObjectKeys:U_,ObjectSetPrototypeOf:yh}=ce();wh.exports=Ve;var so=fi(),Ne=to();yh(Ve.prototype,so.prototype);yh(Ve,so);{let t=U_(Ne.prototype);for(let e=0;e<t.length;e++){let r=t[e];Ve.prototype[r]||(Ve.prototype[r]=Ne.prototype[r])}}function Ve(t){if(!(this instanceof Ve))return new Ve(t);so.call(this,t),Ne.call(this,t),t?(this.allowHalfOpen=t.allowHalfOpen!==!1,t.readable===!1&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),t.writable===!1&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)):this.allowHalfOpen=!0}L_(Ve.prototype,{writable:{__proto__:null,...At(Ne.prototype,"writable")},writableHighWaterMark:{__proto__:null,...At(Ne.prototype,"writableHighWaterMark")},writableObjectMode:{__proto__:null,...At(Ne.prototype,"writableObjectMode")},writableBuffer:{__proto__:null,...At(Ne.prototype,"writableBuffer")},writableLength:{__proto__:null,...At(Ne.prototype,"writableLength")},writableFinished:{__proto__:null,...At(Ne.prototype,"writableFinished")},writableCorked:{__proto__:null,...At(Ne.prototype,"writableCorked")},writableEnded:{__proto__:null,...At(Ne.prototype,"writableEnded")},writableNeedDrain:{__proto__:null,...At(Ne.prototype,"writableNeedDrain")},destroyed:{__proto__:null,get(){return this._readableState===void 0||this._writableState===void 0?!1:this._readableState.destroyed&&this._writableState.destroyed},set(t){this._readableState&&this._writableState&&(this._readableState.destroyed=t,this._writableState.destroyed=t)}}});var io;function bh(){return io===void 0&&(io={}),io}Ve.fromWeb=function(t,e){return bh().newStreamDuplexFromReadableWritablePair(t,e)};Ve.toWeb=function(t){return bh().newReadableWritablePairFromDuplex(t)};var no;Ve.from=function(t){return no||(no=gh()),no(t,"body")}});var ao=L((VT,mh)=>{"use strict";_();E();m();var{ObjectSetPrototypeOf:_h,Symbol:M_}=ce();mh.exports=It;var{ERR_METHOD_NOT_IMPLEMENTED:N_}=Se().codes,lo=nt(),{getHighWaterMark:q_}=Zi();_h(It.prototype,lo.prototype);_h(It,lo);var pi=M_("kCallback");function It(t){if(!(this instanceof It))return new It(t);let e=t?q_(this,t,"readableHighWaterMark",!0):null;e===0&&(t={...t,highWaterMark:null,readableHighWaterMark:e,writableHighWaterMark:t.writableHighWaterMark||0}),lo.call(this,t),this._readableState.sync=!1,this[pi]=null,t&&(typeof t.transform=="function"&&(this._transform=t.transform),typeof t.flush=="function"&&(this._flush=t.flush)),this.on("prefinish",D_)}function oo(t){typeof this._flush=="function"&&!this.destroyed?this._flush((e,r)=>{if(e){t?t(e):this.destroy(e);return}r!=null&&this.push(r),this.push(null),t&&t()}):(this.push(null),t&&t())}function D_(){this._final!==oo&&oo.call(this)}It.prototype._final=oo;It.prototype._transform=function(t,e,r){throw new N_("_transform()")};It.prototype._write=function(t,e,r){let i=this._readableState,n=this._writableState,o=i.length;this._transform(t,e,(s,l)=>{if(s){r(s);return}l!=null&&this.push(l),n.ended||o===i.length||i.length<i.highWaterMark?r():this[pi]=r})};It.prototype._read=function(){if(this[pi]){let t=this[pi];this[pi]=null,t()}}});var fo=L((XT,vh)=>{"use strict";_();E();m();var{ObjectSetPrototypeOf:Eh}=ce();vh.exports=Dr;var uo=ao();Eh(Dr.prototype,uo.prototype);Eh(Dr,uo);function Dr(t){if(!(this instanceof Dr))return new Dr(t);uo.call(this,t)}Dr.prototype._transform=function(t,e,r){r(null,t)}});var gn=L((s2,Rh)=>{_();E();m();var gi=Ut(),{ArrayIsArray:j_,Promise:F_,SymbolAsyncIterator:W_}=ce(),pn=mt(),{once:$_}=Je(),H_=Xt(),Sh=nt(),{aggregateTwoErrors:V_,codes:{ERR_INVALID_ARG_TYPE:_o,ERR_INVALID_RETURN_VALUE:co,ERR_MISSING_ARGS:z_,ERR_STREAM_DESTROYED:K_,ERR_STREAM_PREMATURE_CLOSE:Q_},AbortError:G_}=Se(),{validateFunction:Y_,validateAbortSignal:J_}=li(),{isIterable:ar,isReadable:ho,isReadableNodeStream:dn,isNodeStream:Ah,isTransformStream:jr,isWebStream:X_,isReadableStream:po,isReadableEnded:Z_}=tt(),e0=globalThis.AbortController||Di().AbortController,go,yo;function Ih(t,e,r){let i=!1;t.on("close",()=>{i=!0});let n=pn(t,{readable:e,writable:r},o=>{i=!o});return{destroy:o=>{i||(i=!0,H_.destroyer(t,o||new K_("pipe")))},cleanup:n}}function t0(t){return Y_(t[t.length-1],"streams[stream.length - 1]"),t.pop()}function bo(t){if(ar(t))return t;if(dn(t))return r0(t);throw new _o("val",["Readable","Iterable","AsyncIterable"],t)}async function*r0(t){yo||(yo=fi()),yield*yo.prototype[W_].call(t)}async function hn(t,e,r,{end:i}){let n,o=null,s=c=>{if(c&&(n=c),o){let h=o;o=null,h()}},l=()=>new F_((c,h)=>{n?h(n):o=()=>{n?h(n):c()}});e.on("drain",s);let u=pn(e,{readable:!1},s);try{e.writableNeedDrain&&await l();for await(let c of t)e.write(c)||await l();i&&e.end(),await l(),r()}catch(c){r(n!==c?V_(n,c):c)}finally{u(),e.off("drain",s)}}async function wo(t,e,r,{end:i}){jr(e)&&(e=e.writable);let n=e.getWriter();try{for await(let o of t)await n.ready,n.write(o).catch(()=>{});await n.ready,i&&await n.close(),r()}catch(o){try{await n.abort(o),r(o)}catch(s){r(s)}}}function i0(...t){return Th(t,$_(t0(t)))}function Th(t,e,r){if(t.length===1&&j_(t[0])&&(t=t[0]),t.length<2)throw new z_("streams");let i=new e0,n=i.signal,o=r?.signal,s=[];J_(o,"options.signal");function l(){y(new G_)}o?.addEventListener("abort",l);let u,c,h=[],d=0;function g(P){y(P,--d===0)}function y(P,R){if(P&&(!u||u.code==="ERR_STREAM_PREMATURE_CLOSE")&&(u=P),!(!u&&!R)){for(;h.length;)h.shift()(u);o?.removeEventListener("abort",l),i.abort(),R&&(u||s.forEach(M=>M()),gi.nextTick(e,u,c))}}let w;for(let P=0;P<t.length;P++){let R=t[P],M=P<t.length-1,N=P>0,V=M||r?.end!==!1,Q=P===t.length-1;if(Ah(R)){let z=function(Y){Y&&Y.name!=="AbortError"&&Y.code!=="ERR_STREAM_PREMATURE_CLOSE"&&g(Y)};var I=z;if(V){let{destroy:Y,cleanup:ve}=Ih(R,M,N);h.push(Y),ho(R)&&Q&&s.push(ve)}R.on("error",z),ho(R)&&Q&&s.push(()=>{R.removeListener("error",z)})}if(P===0)if(typeof R=="function"){if(w=R({signal:n}),!ar(w))throw new co("Iterable, AsyncIterable or Stream","source",w)}else ar(R)||dn(R)||jr(R)?w=R:w=Sh.from(R);else if(typeof R=="function"){if(jr(w)){var S;w=bo((S=w)===null||S===void 0?void 0:S.readable)}else w=bo(w);if(w=R(w,{signal:n}),M){if(!ar(w,!0))throw new co("AsyncIterable",`transform[${P-1}]`,w)}else{var A;go||(go=fo());let z=new go({objectMode:!0}),Y=(A=w)===null||A===void 0?void 0:A.then;if(typeof Y=="function")d++,Y.call(w,we=>{c=we,we!=null&&z.write(we),V&&z.end(),gi.nextTick(g)},we=>{z.destroy(we),gi.nextTick(g,we)});else if(ar(w,!0))d++,hn(w,z,g,{end:V});else if(po(w)||jr(w)){let we=w.readable||w;d++,hn(we,z,g,{end:V})}else throw new co("AsyncIterable or Promise","destination",w);w=z;let{destroy:ve,cleanup:ni}=Ih(w,!1,!0);h.push(ve),Q&&s.push(ni)}}else if(Ah(R)){if(dn(w)){d+=2;let z=n0(w,R,g,{end:V});ho(R)&&Q&&s.push(z)}else if(jr(w)||po(w)){let z=w.readable||w;d++,hn(z,R,g,{end:V})}else if(ar(w))d++,hn(w,R,g,{end:V});else throw new _o("val",["Readable","Iterable","AsyncIterable","ReadableStream","TransformStream"],w);w=R}else if(X_(R)){if(dn(w))d++,wo(bo(w),R,g,{end:V});else if(po(w)||ar(w))d++,wo(w,R,g,{end:V});else if(jr(w))d++,wo(w.readable,R,g,{end:V});else throw new _o("val",["Readable","Iterable","AsyncIterable","ReadableStream","TransformStream"],w);w=R}else w=Sh.from(R)}return(n!=null&&n.aborted||o!=null&&o.aborted)&&gi.nextTick(l),w}function n0(t,e,r,{end:i}){let n=!1;if(e.on("close",()=>{n||r(new Q_)}),t.pipe(e,{end:!1}),i){let s=function(){n=!0,e.end()};var o=s;Z_(t)?gi.nextTick(s):t.once("end",s)}else r();return pn(t,{readable:!0,writable:!1},s=>{let l=t._readableState;s&&s.code==="ERR_STREAM_PREMATURE_CLOSE"&&l&&l.ended&&!l.errored&&!l.errorEmitted?t.once("end",r).once("error",r):r(s)}),pn(e,{readable:!1,writable:!0},r)}Rh.exports={pipelineImpl:Th,pipeline:i0}});var Eo=L((h2,kh)=>{"use strict";_();E();m();var{pipeline:s0}=gn(),yn=nt(),{destroyer:o0}=Xt(),{isNodeStream:bn,isReadable:Ch,isWritable:Bh,isWebStream:mo,isTransformStream:ur,isWritableStream:Ph,isReadableStream:xh}=tt(),{AbortError:l0,codes:{ERR_INVALID_ARG_VALUE:Oh,ERR_MISSING_ARGS:a0}}=Se(),u0=mt();kh.exports=function(...e){if(e.length===0)throw new a0("streams");if(e.length===1)return yn.from(e[0]);let r=[...e];if(typeof e[0]=="function"&&(e[0]=yn.from(e[0])),typeof e[e.length-1]=="function"){let y=e.length-1;e[y]=yn.from(e[y])}for(let y=0;y<e.length;++y)if(!(!bn(e[y])&&!mo(e[y]))){if(y<e.length-1&&!(Ch(e[y])||xh(e[y])||ur(e[y])))throw new Oh(`streams[${y}]`,r[y],"must be readable");if(y>0&&!(Bh(e[y])||Ph(e[y])||ur(e[y])))throw new Oh(`streams[${y}]`,r[y],"must be writable")}let i,n,o,s,l;function u(y){let w=s;s=null,w?w(y):y?l.destroy(y):!g&&!d&&l.destroy()}let c=e[0],h=s0(e,u),d=!!(Bh(c)||Ph(c)||ur(c)),g=!!(Ch(h)||xh(h)||ur(h));if(l=new yn({writableObjectMode:!!(c!=null&&c.writableObjectMode),readableObjectMode:!!(h!=null&&h.writableObjectMode),writable:d,readable:g}),d){if(bn(c))l._write=function(w,S,A){c.write(w,S)?A():i=A},l._final=function(w){c.end(),n=w},c.on("drain",function(){if(i){let w=i;i=null,w()}});else if(mo(c)){let S=(ur(c)?c.writable:c).getWriter();l._write=async function(A,I,P){try{await S.ready,S.write(A).catch(()=>{}),P()}catch(R){P(R)}},l._final=async function(A){try{await S.ready,S.close().catch(()=>{}),n=A}catch(I){A(I)}}}let y=ur(h)?h.readable:h;u0(y,()=>{if(n){let w=n;n=null,w()}})}if(g){if(bn(h))h.on("readable",function(){if(o){let y=o;o=null,y()}}),h.on("end",function(){l.push(null)}),l._read=function(){for(;;){let y=h.read();if(y===null){o=l._read;return}if(!l.push(y))return}};else if(mo(h)){let w=(ur(h)?h.readable:h).getReader();l._read=async function(){for(;;)try{let{value:S,done:A}=await w.read();if(!l.push(S))return;if(A){l.push(null);return}}catch{return}}}}return l._destroy=function(y,w){!y&&s!==null&&(y=new l0),o=null,i=null,n=null,s===null?w(y):(s=w,bn(h)&&o0(h,y))},l}});var Fh=L((_2,Ao)=>{"use strict";_();E();m();var Nh=globalThis.AbortController||Di().AbortController,{codes:{ERR_INVALID_ARG_VALUE:f0,ERR_INVALID_ARG_TYPE:yi,ERR_MISSING_ARGS:c0,ERR_OUT_OF_RANGE:h0},AbortError:st}=Se(),{validateAbortSignal:fr,validateInteger:d0,validateObject:cr}=li(),p0=ce().Symbol("kWeak"),{finished:g0}=mt(),y0=Eo(),{addAbortSignalNoValidate:b0}=ai(),{isWritable:w0,isNodeStream:_0}=tt(),{ArrayPrototypePush:m0,MathFloor:E0,Number:v0,NumberIsNaN:S0,Promise:Lh,PromiseReject:Uh,PromisePrototypeThen:A0,Symbol:qh}=ce(),wn=qh("kEmpty"),Mh=qh("kEof");function I0(t,e){if(e!=null&&cr(e,"options"),e?.signal!=null&&fr(e.signal,"options.signal"),_0(t)&&!w0(t))throw new f0("stream",t,"must be writable");let r=y0(this,t);return e!=null&&e.signal&&b0(e.signal,r),r}function _n(t,e){if(typeof t!="function")throw new yi("fn",["Function","AsyncFunction"],t);e!=null&&cr(e,"options"),e?.signal!=null&&fr(e.signal,"options.signal");let r=1;return e?.concurrency!=null&&(r=E0(e.concurrency)),d0(r,"concurrency",1),async function*(){var n,o;let s=new Nh,l=this,u=[],c=s.signal,h={signal:c},d=()=>s.abort();e!=null&&(n=e.signal)!==null&&n!==void 0&&n.aborted&&d(),e==null||(o=e.signal)===null||o===void 0||o.addEventListener("abort",d);let g,y,w=!1;function S(){w=!0}async function A(){try{for await(let R of l){var I;if(w)return;if(c.aborted)throw new st;try{R=t(R,h)}catch(M){R=Uh(M)}R!==wn&&(typeof((I=R)===null||I===void 0?void 0:I.catch)=="function"&&R.catch(S),u.push(R),g&&(g(),g=null),!w&&u.length&&u.length>=r&&await new Lh(M=>{y=M}))}u.push(Mh)}catch(R){let M=Uh(R);A0(M,void 0,S),u.push(M)}finally{var P;w=!0,g&&(g(),g=null),e==null||(P=e.signal)===null||P===void 0||P.removeEventListener("abort",d)}}A();try{for(;;){for(;u.length>0;){let I=await u[0];if(I===Mh)return;if(c.aborted)throw new st;I!==wn&&(yield I),u.shift(),y&&(y(),y=null)}await new Lh(I=>{g=I})}}finally{s.abort(),w=!0,y&&(y(),y=null)}}.call(this)}function T0(t=void 0){return t!=null&&cr(t,"options"),t?.signal!=null&&fr(t.signal,"options.signal"),async function*(){let r=0;for await(let n of this){var i;if(t!=null&&(i=t.signal)!==null&&i!==void 0&&i.aborted)throw new st({cause:t.signal.reason});yield[r++,n]}}.call(this)}async function Dh(t,e=void 0){for await(let r of So.call(this,t,e))return!0;return!1}async function R0(t,e=void 0){if(typeof t!="function")throw new yi("fn",["Function","AsyncFunction"],t);return!await Dh.call(this,async(...r)=>!await t(...r),e)}async function C0(t,e){for await(let r of So.call(this,t,e))return r}async function B0(t,e){if(typeof t!="function")throw new yi("fn",["Function","AsyncFunction"],t);async function r(i,n){return await t(i,n),wn}for await(let i of _n.call(this,r,e));}function So(t,e){if(typeof t!="function")throw new yi("fn",["Function","AsyncFunction"],t);async function r(i,n){return await t(i,n)?i:wn}return _n.call(this,r,e)}var vo=class extends c0{constructor(){super("reduce"),this.message="Reduce of an empty stream requires an initial value"}};async function P0(t,e,r){var i;if(typeof t!="function")throw new yi("reducer",["Function","AsyncFunction"],t);r!=null&&cr(r,"options"),r?.signal!=null&&fr(r.signal,"options.signal");let n=arguments.length>1;if(r!=null&&(i=r.signal)!==null&&i!==void 0&&i.aborted){let c=new st(void 0,{cause:r.signal.reason});throw this.once("error",()=>{}),await g0(this.destroy(c)),c}let o=new Nh,s=o.signal;if(r!=null&&r.signal){let c={once:!0,[p0]:this};r.signal.addEventListener("abort",()=>o.abort(),c)}let l=!1;try{for await(let c of this){var u;if(l=!0,r!=null&&(u=r.signal)!==null&&u!==void 0&&u.aborted)throw new st;n?e=await t(e,c,{signal:s}):(e=c,n=!0)}if(!l&&!n)throw new vo}finally{o.abort()}return e}async function x0(t){t!=null&&cr(t,"options"),t?.signal!=null&&fr(t.signal,"options.signal");let e=[];for await(let i of this){var r;if(t!=null&&(r=t.signal)!==null&&r!==void 0&&r.aborted)throw new st(void 0,{cause:t.signal.reason});m0(e,i)}return e}function O0(t,e){let r=_n.call(this,t,e);return async function*(){for await(let n of r)yield*n}.call(this)}function jh(t){if(t=v0(t),S0(t))return 0;if(t<0)throw new h0("number",">= 0",t);return t}function k0(t,e=void 0){return e!=null&&cr(e,"options"),e?.signal!=null&&fr(e.signal,"options.signal"),t=jh(t),async function*(){var i;if(e!=null&&(i=e.signal)!==null&&i!==void 0&&i.aborted)throw new st;for await(let o of this){var n;if(e!=null&&(n=e.signal)!==null&&n!==void 0&&n.aborted)throw new st;t--<=0&&(yield o)}}.call(this)}function L0(t,e=void 0){return e!=null&&cr(e,"options"),e?.signal!=null&&fr(e.signal,"options.signal"),t=jh(t),async function*(){var i;if(e!=null&&(i=e.signal)!==null&&i!==void 0&&i.aborted)throw new st;for await(let o of this){var n;if(e!=null&&(n=e.signal)!==null&&n!==void 0&&n.aborted)throw new st;if(t-- >0)yield o;else return}}.call(this)}Ao.exports.streamReturningOperators={asIndexedPairs:T0,drop:k0,filter:So,flatMap:O0,map:_n,take:L0,compose:I0};Ao.exports.promiseReturningOperators={every:R0,forEach:B0,reduce:P0,toArray:x0,some:Dh,find:C0}});var Io=L((T2,Wh)=>{"use strict";_();E();m();var{ArrayPrototypePop:U0,Promise:M0}=ce(),{isIterable:N0,isNodeStream:q0,isWebStream:D0}=tt(),{pipelineImpl:j0}=gn(),{finished:F0}=mt();To();function W0(...t){return new M0((e,r)=>{let i,n,o=t[t.length-1];if(o&&typeof o=="object"&&!q0(o)&&!N0(o)&&!D0(o)){let s=U0(t);i=s.signal,n=s.end}j0(t,(s,l)=>{s?r(s):e(l)},{signal:i,end:n})})}Wh.exports={finished:F0,pipeline:W0}});var To=L((k2,Jh)=>{_();E();m();var{Buffer:$0}=(ye(),Z(me)),{ObjectDefineProperty:Tt,ObjectKeys:Vh,ReflectApply:zh}=ce(),{promisify:{custom:Kh}}=Je(),{streamReturningOperators:$h,promiseReturningOperators:Hh}=Fh(),{codes:{ERR_ILLEGAL_CONSTRUCTOR:Qh}}=Se(),H0=Eo(),{pipeline:Gh}=gn(),{destroyer:V0}=Xt(),Yh=mt(),Ro=Io(),Co=tt(),ae=Jh.exports=Yi().Stream;ae.isDisturbed=Co.isDisturbed;ae.isErrored=Co.isErrored;ae.isReadable=Co.isReadable;ae.Readable=fi();for(let t of Vh($h)){let r=function(...i){if(new.target)throw Qh();return ae.Readable.from(zh(e,this,i))};Bo=r;let e=$h[t];Tt(r,"name",{__proto__:null,value:e.name}),Tt(r,"length",{__proto__:null,value:e.length}),Tt(ae.Readable.prototype,t,{__proto__:null,value:r,enumerable:!1,configurable:!0,writable:!0})}var Bo;for(let t of Vh(Hh)){let r=function(...n){if(new.target)throw Qh();return zh(e,this,n)};Bo=r;let e=Hh[t];Tt(r,"name",{__proto__:null,value:e.name}),Tt(r,"length",{__proto__:null,value:e.length}),Tt(ae.Readable.prototype,t,{__proto__:null,value:r,enumerable:!1,configurable:!0,writable:!0})}var Bo;ae.Writable=to();ae.Duplex=nt();ae.Transform=ao();ae.PassThrough=fo();ae.pipeline=Gh;var{addAbortSignal:z0}=ai();ae.addAbortSignal=z0;ae.finished=Yh;ae.destroy=V0;ae.compose=H0;Tt(ae,"promises",{__proto__:null,configurable:!0,enumerable:!0,get(){return Ro}});Tt(Gh,Kh,{__proto__:null,enumerable:!0,get(){return Ro.pipeline}});Tt(Yh,Kh,{__proto__:null,enumerable:!0,get(){return Ro.finished}});ae.Stream=ae;ae._isUint8Array=function(e){return e instanceof Uint8Array};ae._uint8ArrayToBuffer=function(e){return $0.from(e.buffer,e.byteOffset,e.byteLength)}});var qt=L((j2,ue)=>{"use strict";_();E();m();var he=To(),K0=Io(),Q0=he.Readable.destroy;ue.exports=he.Readable;ue.exports._uint8ArrayToBuffer=he._uint8ArrayToBuffer;ue.exports._isUint8Array=he._isUint8Array;ue.exports.isDisturbed=he.isDisturbed;ue.exports.isErrored=he.isErrored;ue.exports.isReadable=he.isReadable;ue.exports.Readable=he.Readable;ue.exports.Writable=he.Writable;ue.exports.Duplex=he.Duplex;ue.exports.Transform=he.Transform;ue.exports.PassThrough=he.PassThrough;ue.exports.addAbortSignal=he.addAbortSignal;ue.exports.finished=he.finished;ue.exports.destroy=he.destroy;ue.exports.destroy=Q0;ue.exports.pipeline=he.pipeline;ue.exports.compose=he.compose;Object.defineProperty(he,"promises",{configurable:!0,enumerable:!0,get(){return K0}});ue.exports.Stream=he.Stream;ue.exports.default=ue.exports});var Xh=L((K2,Po)=>{_();E();m();typeof Object.create=="function"?Po.exports=function(e,r){r&&(e.super_=r,e.prototype=Object.create(r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:Po.exports=function(e,r){if(r){e.super_=r;var i=function(){};i.prototype=r.prototype,e.prototype=new i,e.prototype.constructor=e}}});var td=L((eR,ed)=>{"use strict";_();E();m();var{Buffer:ze}=(ye(),Z(me)),Zh=Symbol.for("BufferList");function ee(t){if(!(this instanceof ee))return new ee(t);ee._init.call(this,t)}ee._init=function(e){Object.defineProperty(this,Zh,{value:!0}),this._bufs=[],this.length=0,e&&this.append(e)};ee.prototype._new=function(e){return new ee(e)};ee.prototype._offset=function(e){if(e===0)return[0,0];let r=0;for(let i=0;i<this._bufs.length;i++){let n=r+this._bufs[i].length;if(e<n||i===this._bufs.length-1)return[i,e-r];r=n}};ee.prototype._reverseOffset=function(t){let e=t[0],r=t[1];for(let i=0;i<e;i++)r+=this._bufs[i].length;return r};ee.prototype.get=function(e){if(e>this.length||e<0)return;let r=this._offset(e);return this._bufs[r[0]][r[1]]};ee.prototype.slice=function(e,r){return typeof e=="number"&&e<0&&(e+=this.length),typeof r=="number"&&r<0&&(r+=this.length),this.copy(null,0,e,r)};ee.prototype.copy=function(e,r,i,n){if((typeof i!="number"||i<0)&&(i=0),(typeof n!="number"||n>this.length)&&(n=this.length),i>=this.length||n<=0)return e||ze.alloc(0);let o=!!e,s=this._offset(i),l=n-i,u=l,c=o&&r||0,h=s[1];if(i===0&&n===this.length){if(!o)return this._bufs.length===1?this._bufs[0]:ze.concat(this._bufs,this.length);for(let d=0;d<this._bufs.length;d++)this._bufs[d].copy(e,c),c+=this._bufs[d].length;return e}if(u<=this._bufs[s[0]].length-h)return o?this._bufs[s[0]].copy(e,r,h,h+u):this._bufs[s[0]].slice(h,h+u);o||(e=ze.allocUnsafe(l));for(let d=s[0];d<this._bufs.length;d++){let g=this._bufs[d].length-h;if(u>g)this._bufs[d].copy(e,c,h),c+=g;else{this._bufs[d].copy(e,c,h,h+u),c+=g;break}u-=g,h&&(h=0)}return e.length>c?e.slice(0,c):e};ee.prototype.shallowSlice=function(e,r){if(e=e||0,r=typeof r!="number"?this.length:r,e<0&&(e+=this.length),r<0&&(r+=this.length),e===r)return this._new();let i=this._offset(e),n=this._offset(r),o=this._bufs.slice(i[0],n[0]+1);return n[1]===0?o.pop():o[o.length-1]=o[o.length-1].slice(0,n[1]),i[1]!==0&&(o[0]=o[0].slice(i[1])),this._new(o)};ee.prototype.toString=function(e,r,i){return this.slice(r,i).toString(e)};ee.prototype.consume=function(e){if(e=Math.trunc(e),Number.isNaN(e)||e<=0)return this;for(;this._bufs.length;)if(e>=this._bufs[0].length)e-=this._bufs[0].length,this.length-=this._bufs[0].length,this._bufs.shift();else{this._bufs[0]=this._bufs[0].slice(e),this.length-=e;break}return this};ee.prototype.duplicate=function(){let e=this._new();for(let r=0;r<this._bufs.length;r++)e.append(this._bufs[r]);return e};ee.prototype.append=function(e){if(e==null)return this;if(e.buffer)this._appendBuffer(ze.from(e.buffer,e.byteOffset,e.byteLength));else if(Array.isArray(e))for(let r=0;r<e.length;r++)this.append(e[r]);else if(this._isBufferList(e))for(let r=0;r<e._bufs.length;r++)this.append(e._bufs[r]);else typeof e=="number"&&(e=e.toString()),this._appendBuffer(ze.from(e));return this};ee.prototype._appendBuffer=function(e){this._bufs.push(e),this.length+=e.length};ee.prototype.indexOf=function(t,e,r){if(r===void 0&&typeof e=="string"&&(r=e,e=void 0),typeof t=="function"||Array.isArray(t))throw new TypeError('The "value" argument must be one of type string, Buffer, BufferList, or Uint8Array.');if(typeof t=="number"?t=ze.from([t]):typeof t=="string"?t=ze.from(t,r):this._isBufferList(t)?t=t.slice():Array.isArray(t.buffer)?t=ze.from(t.buffer,t.byteOffset,t.byteLength):ze.isBuffer(t)||(t=ze.from(t)),e=Number(e||0),isNaN(e)&&(e=0),e<0&&(e=this.length+e),e<0&&(e=0),t.length===0)return e>this.length?this.length:e;let i=this._offset(e),n=i[0],o=i[1];for(;n<this._bufs.length;n++){let s=this._bufs[n];for(;o<s.length;)if(s.length-o>=t.length){let u=s.indexOf(t,o);if(u!==-1)return this._reverseOffset([n,u]);o=s.length-t.length+1}else{let u=this._reverseOffset([n,o]);if(this._match(u,t))return u;o++}o=0}return-1};ee.prototype._match=function(t,e){if(this.length-t<e.length)return!1;for(let r=0;r<e.length;r++)if(this.get(t+r)!==e[r])return!1;return!0};(function(){let t={readDoubleBE:8,readDoubleLE:8,readFloatBE:4,readFloatLE:4,readBigInt64BE:8,readBigInt64LE:8,readBigUInt64BE:8,readBigUInt64LE:8,readInt32BE:4,readInt32LE:4,readUInt32BE:4,readUInt32LE:4,readInt16BE:2,readInt16LE:2,readUInt16BE:2,readUInt16LE:2,readInt8:1,readUInt8:1,readIntBE:null,readIntLE:null,readUIntBE:null,readUIntLE:null};for(let e in t)(function(r){t[r]===null?ee.prototype[r]=function(i,n){return this.slice(i,i+n)[r](0,n)}:ee.prototype[r]=function(i=0){return this.slice(i,i+t[r])[r](0)}})(e)})();ee.prototype._isBufferList=function(e){return e instanceof ee||ee.isBufferList(e)};ee.isBufferList=function(e){return e!=null&&e[Zh]};ed.exports=ee});var rd=L((lR,mn)=>{"use strict";_();E();m();var xo=qt().Duplex,G0=Xh(),bi=td();function Ee(t){if(!(this instanceof Ee))return new Ee(t);if(typeof t=="function"){this._callback=t;let e=function(i){this._callback&&(this._callback(i),this._callback=null)}.bind(this);this.on("pipe",function(i){i.on("error",e)}),this.on("unpipe",function(i){i.removeListener("error",e)}),t=null}bi._init.call(this,t),xo.call(this)}G0(Ee,xo);Object.assign(Ee.prototype,bi.prototype);Ee.prototype._new=function(e){return new Ee(e)};Ee.prototype._write=function(e,r,i){this._appendBuffer(e),typeof i=="function"&&i()};Ee.prototype._read=function(e){if(!this.length)return this.push(null);e=Math.min(e,this.length),this.push(this.slice(0,e)),this.consume(e)};Ee.prototype.end=function(e){xo.prototype.end.call(this,e),this._callback&&(this._callback(null,this.slice()),this._callback=null)};Ee.prototype._destroy=function(e,r){this._bufs.length=0,this.length=0,r(e)};Ee.prototype._isBufferList=function(e){return e instanceof Ee||e instanceof bi||Ee.isBufferList(e)};Ee.isBufferList=bi.isBufferList;mn.exports=Ee;mn.exports.BufferListStream=Ee;mn.exports.BufferList=bi});var nd=L((pR,id)=>{_();E();m();var Oo=class{constructor(){this.cmd=null,this.retain=!1,this.qos=0,this.dup=!1,this.length=-1,this.topic=null,this.payload=null}};id.exports=Oo});var ko=L((ER,sd)=>{_();E();m();var U=sd.exports,{Buffer:xe}=(ye(),Z(me));U.types={0:"reserved",1:"connect",2:"connack",3:"publish",4:"puback",5:"pubrec",6:"pubrel",7:"pubcomp",8:"subscribe",9:"suback",10:"unsubscribe",11:"unsuback",12:"pingreq",13:"pingresp",14:"disconnect",15:"auth"};U.requiredHeaderFlags={1:0,2:0,4:0,5:0,6:2,7:0,8:2,9:0,10:2,11:0,12:0,13:0,14:0,15:0};U.requiredHeaderFlagsErrors={};for(let t in U.requiredHeaderFlags){let e=U.requiredHeaderFlags[t];U.requiredHeaderFlagsErrors[t]="Invalid header flag bits, must be 0x"+e.toString(16)+" for "+U.types[t]+" packet"}U.codes={};for(let t in U.types){let e=U.types[t];U.codes[e]=t}U.CMD_SHIFT=4;U.CMD_MASK=240;U.DUP_MASK=8;U.QOS_MASK=3;U.QOS_SHIFT=1;U.RETAIN_MASK=1;U.VARBYTEINT_MASK=127;U.VARBYTEINT_FIN_MASK=128;U.VARBYTEINT_MAX=268435455;U.SESSIONPRESENT_MASK=1;U.SESSIONPRESENT_HEADER=xe.from([U.SESSIONPRESENT_MASK]);U.CONNACK_HEADER=xe.from([U.codes.connack<<U.CMD_SHIFT]);U.USERNAME_MASK=128;U.PASSWORD_MASK=64;U.WILL_RETAIN_MASK=32;U.WILL_QOS_MASK=24;U.WILL_QOS_SHIFT=3;U.WILL_FLAG_MASK=4;U.CLEAN_SESSION_MASK=2;U.CONNECT_HEADER=xe.from([U.codes.connect<<U.CMD_SHIFT]);U.properties={sessionExpiryInterval:17,willDelayInterval:24,receiveMaximum:33,maximumPacketSize:39,topicAliasMaximum:34,requestResponseInformation:25,requestProblemInformation:23,userProperties:38,authenticationMethod:21,authenticationData:22,payloadFormatIndicator:1,messageExpiryInterval:2,contentType:3,responseTopic:8,correlationData:9,maximumQoS:36,retainAvailable:37,assignedClientIdentifier:18,reasonString:31,wildcardSubscriptionAvailable:40,subscriptionIdentifiersAvailable:41,sharedSubscriptionAvailable:42,serverKeepAlive:19,responseInformation:26,serverReference:28,topicAlias:35,subscriptionIdentifier:11};U.propertiesCodes={};for(let t in U.properties){let e=U.properties[t];U.propertiesCodes[e]=t}U.propertiesTypes={sessionExpiryInterval:"int32",willDelayInterval:"int32",receiveMaximum:"int16",maximumPacketSize:"int32",topicAliasMaximum:"int16",requestResponseInformation:"byte",requestProblemInformation:"byte",userProperties:"pair",authenticationMethod:"string",authenticationData:"binary",payloadFormatIndicator:"byte",messageExpiryInterval:"int32",contentType:"string",responseTopic:"string",correlationData:"binary",maximumQoS:"int8",retainAvailable:"byte",assignedClientIdentifier:"string",reasonString:"string",wildcardSubscriptionAvailable:"byte",subscriptionIdentifiersAvailable:"byte",sharedSubscriptionAvailable:"byte",serverKeepAlive:"int16",responseInformation:"string",serverReference:"string",topicAlias:"int16",subscriptionIdentifier:"var"};function Dt(t){return[0,1,2].map(e=>[0,1].map(r=>[0,1].map(i=>{let n=xe.alloc(1);return n.writeUInt8(U.codes[t]<<U.CMD_SHIFT|(r?U.DUP_MASK:0)|e<<U.QOS_SHIFT|i,0,!0),n})))}U.PUBLISH_HEADER=Dt("publish");U.SUBSCRIBE_HEADER=Dt("subscribe");U.SUBSCRIBE_OPTIONS_QOS_MASK=3;U.SUBSCRIBE_OPTIONS_NL_MASK=1;U.SUBSCRIBE_OPTIONS_NL_SHIFT=2;U.SUBSCRIBE_OPTIONS_RAP_MASK=1;U.SUBSCRIBE_OPTIONS_RAP_SHIFT=3;U.SUBSCRIBE_OPTIONS_RH_MASK=3;U.SUBSCRIBE_OPTIONS_RH_SHIFT=4;U.SUBSCRIBE_OPTIONS_RH=[0,16,32];U.SUBSCRIBE_OPTIONS_NL=4;U.SUBSCRIBE_OPTIONS_RAP=8;U.SUBSCRIBE_OPTIONS_QOS=[0,1,2];U.UNSUBSCRIBE_HEADER=Dt("unsubscribe");U.ACKS={unsuback:Dt("unsuback"),puback:Dt("puback"),pubcomp:Dt("pubcomp"),pubrel:Dt("pubrel"),pubrec:Dt("pubrec")};U.SUBACK_HEADER=xe.from([U.codes.suback<<U.CMD_SHIFT]);U.VERSION3=xe.from([3]);U.VERSION4=xe.from([4]);U.VERSION5=xe.from([5]);U.VERSION131=xe.from([131]);U.VERSION132=xe.from([132]);U.QOS=[0,1,2].map(t=>xe.from([t]));U.EMPTY={pingreq:xe.from([U.codes.pingreq<<4,0]),pingresp:xe.from([U.codes.pingresp<<4,0]),disconnect:xe.from([U.codes.disconnect<<4,0])};U.MQTT5_PUBACK_PUBREC_CODES={0:"Success",16:"No matching subscribers",128:"Unspecified error",131:"Implementation specific error",135:"Not authorized",144:"Topic Name invalid",145:"Packet identifier in use",151:"Quota exceeded",153:"Payload format invalid"};U.MQTT5_PUBREL_PUBCOMP_CODES={0:"Success",146:"Packet Identifier not found"};U.MQTT5_SUBACK_CODES={0:"Granted QoS 0",1:"Granted QoS 1",2:"Granted QoS 2",128:"Unspecified error",131:"Implementation specific error",135:"Not authorized",143:"Topic Filter invalid",145:"Packet Identifier in use",151:"Quota exceeded",158:"Shared Subscriptions not supported",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"};U.MQTT5_UNSUBACK_CODES={0:"Success",17:"No subscription existed",128:"Unspecified error",131:"Implementation specific error",135:"Not authorized",143:"Topic Filter invalid",145:"Packet Identifier in use"};U.MQTT5_DISCONNECT_CODES={0:"Normal disconnection",4:"Disconnect with Will Message",128:"Unspecified error",129:"Malformed Packet",130:"Protocol Error",131:"Implementation specific error",135:"Not authorized",137:"Server busy",139:"Server shutting down",141:"Keep Alive timeout",142:"Session taken over",143:"Topic Filter invalid",144:"Topic Name invalid",147:"Receive Maximum exceeded",148:"Topic Alias invalid",149:"Packet too large",150:"Message rate too high",151:"Quota exceeded",152:"Administrative action",153:"Payload format invalid",154:"Retain not supported",155:"QoS not supported",156:"Use another server",157:"Server moved",158:"Shared Subscriptions not supported",159:"Connection rate exceeded",160:"Maximum connect time",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"};U.MQTT5_AUTH_CODES={0:"Success",24:"Continue authentication",25:"Re-authenticate"}});var ld=L((CR,od)=>{_();E();m();var Fr=1e3,Wr=Fr*60,$r=Wr*60,hr=$r*24,Y0=hr*7,J0=hr*365.25;od.exports=function(t,e){e=e||{};var r=typeof t;if(r==="string"&&t.length>0)return X0(t);if(r==="number"&&isFinite(t))return e.long?em(t):Z0(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))};function X0(t){if(t=String(t),!(t.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(e){var r=parseFloat(e[1]),i=(e[2]||"ms").toLowerCase();switch(i){case"years":case"year":case"yrs":case"yr":case"y":return r*J0;case"weeks":case"week":case"w":return r*Y0;case"days":case"day":case"d":return r*hr;case"hours":case"hour":case"hrs":case"hr":case"h":return r*$r;case"minutes":case"minute":case"mins":case"min":case"m":return r*Wr;case"seconds":case"second":case"secs":case"sec":case"s":return r*Fr;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}function Z0(t){var e=Math.abs(t);return e>=hr?Math.round(t/hr)+"d":e>=$r?Math.round(t/$r)+"h":e>=Wr?Math.round(t/Wr)+"m":e>=Fr?Math.round(t/Fr)+"s":t+"ms"}function em(t){var e=Math.abs(t);return e>=hr?En(t,e,hr,"day"):e>=$r?En(t,e,$r,"hour"):e>=Wr?En(t,e,Wr,"minute"):e>=Fr?En(t,e,Fr,"second"):t+" ms"}function En(t,e,r,i){var n=e>=r*1.5;return Math.round(t/r)+" "+i+(n?"s":"")}});var ud=L((UR,ad)=>{_();E();m();function tm(t){r.debug=r,r.default=r,r.coerce=u,r.disable=o,r.enable=n,r.enabled=s,r.humanize=ld(),r.destroy=c,Object.keys(t).forEach(h=>{r[h]=t[h]}),r.names=[],r.skips=[],r.formatters={};function e(h){let d=0;for(let g=0;g<h.length;g++)d=(d<<5)-d+h.charCodeAt(g),d|=0;return r.colors[Math.abs(d)%r.colors.length]}r.selectColor=e;function r(h){let d,g=null,y,w;function S(...A){if(!S.enabled)return;let I=S,P=Number(new Date),R=P-(d||P);I.diff=R,I.prev=d,I.curr=P,d=P,A[0]=r.coerce(A[0]),typeof A[0]!="string"&&A.unshift("%O");let M=0;A[0]=A[0].replace(/%([a-zA-Z%])/g,(V,Q)=>{if(V==="%%")return"%";M++;let z=r.formatters[Q];if(typeof z=="function"){let Y=A[M];V=z.call(I,Y),A.splice(M,1),M--}return V}),r.formatArgs.call(I,A),(I.log||r.log).apply(I,A)}return S.namespace=h,S.useColors=r.useColors(),S.color=r.selectColor(h),S.extend=i,S.destroy=r.destroy,Object.defineProperty(S,"enabled",{enumerable:!0,configurable:!1,get:()=>g!==null?g:(y!==r.namespaces&&(y=r.namespaces,w=r.enabled(h)),w),set:A=>{g=A}}),typeof r.init=="function"&&r.init(S),S}function i(h,d){let g=r(this.namespace+(typeof d>"u"?":":d)+h);return g.log=this.log,g}function n(h){r.save(h),r.namespaces=h,r.names=[],r.skips=[];let d,g=(typeof h=="string"?h:"").split(/[\s,]+/),y=g.length;for(d=0;d<y;d++)g[d]&&(h=g[d].replace(/\*/g,".*?"),h[0]==="-"?r.skips.push(new RegExp("^"+h.slice(1)+"$")):r.names.push(new RegExp("^"+h+"$")))}function o(){let h=[...r.names.map(l),...r.skips.map(l).map(d=>"-"+d)].join(",");return r.enable(""),h}function s(h){if(h[h.length-1]==="*")return!0;let d,g;for(d=0,g=r.skips.length;d<g;d++)if(r.skips[d].test(h))return!1;for(d=0,g=r.names.length;d<g;d++)if(r.names[d].test(h))return!0;return!1}function l(h){return h.toString().substring(2,h.toString().length-2).replace(/\.\*\?$/,"*")}function u(h){return h instanceof Error?h.stack||h.message:h}function c(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return r.enable(r.load()),r}ad.exports=tm});var ot=L((ke,vn)=>{_();E();m();ke.formatArgs=im;ke.save=nm;ke.load=sm;ke.useColors=rm;ke.storage=om();ke.destroy=(()=>{let t=!1;return()=>{t||(t=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();ke.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function rm(){return typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function im(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+vn.exports.humanize(this.diff),!this.useColors)return;let e="color: "+this.color;t.splice(1,0,e,"color: inherit");let r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,n=>{n!=="%%"&&(r++,n==="%c"&&(i=r))}),t.splice(i,0,e)}ke.log=console.debug||console.log||(()=>{});function nm(t){try{t?ke.storage.setItem("debug",t):ke.storage.removeItem("debug")}catch{}}function sm(){let t;try{t=ke.storage.getItem("debug")}catch{}return!t&&typeof C<"u"&&"env"in C&&(t=C.env.DEBUG),t}function om(){try{return localStorage}catch{}}vn.exports=ud()(ke);var{formatters:lm}=vn.exports;lm.j=function(t){try{return JSON.stringify(t)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}});var hd=L((QR,cd)=>{_();E();m();var am=rd(),{EventEmitter:um}=(er(),Z(Zt)),fd=nd(),H=ko(),D=ot()("mqtt-packet:parser"),Lo=class t extends um{constructor(){super(),this.parser=this.constructor.parser}static parser(e){return this instanceof t?(this.settings=e||{},this._states=["_parseHeader","_parseLength","_parsePayload","_newPacket"],this._resetState(),this):new t().parser(e)}_resetState(){D("_resetState: resetting packet, error, _list, and _stateCounter"),this.packet=new fd,this.error=null,this._list=am(),this._stateCounter=0}parse(e){for(this.error&&this._resetState(),this._list.append(e),D("parse: current state: %s",this._states[this._stateCounter]);(this.packet.length!==-1||this._list.length>0)&&this[this._states[this._stateCounter]]()&&!this.error;)this._stateCounter++,D("parse: state complete. _stateCounter is now: %d",this._stateCounter),D("parse: packet.length: %d, buffer list length: %d",this.packet.length,this._list.length),this._stateCounter>=this._states.length&&(this._stateCounter=0);return D("parse: exited while loop. packet: %d, buffer list length: %d",this.packet.length,this._list.length),this._list.length}_parseHeader(){let e=this._list.readUInt8(0),r=e>>H.CMD_SHIFT;this.packet.cmd=H.types[r];let i=e&15,n=H.requiredHeaderFlags[r];return n!=null&&i!==n?this._emitError(new Error(H.requiredHeaderFlagsErrors[r])):(this.packet.retain=(e&H.RETAIN_MASK)!==0,this.packet.qos=e>>H.QOS_SHIFT&H.QOS_MASK,this.packet.qos>2?this._emitError(new Error("Packet must not have both QoS bits set to 1")):(this.packet.dup=(e&H.DUP_MASK)!==0,D("_parseHeader: packet: %o",this.packet),this._list.consume(1),!0))}_parseLength(){let e=this._parseVarByteNum(!0);return e&&(this.packet.length=e.value,this._list.consume(e.bytes)),D("_parseLength %d",e.value),!!e}_parsePayload(){D("_parsePayload: payload %O",this._list);let e=!1;if(this.packet.length===0||this._list.length>=this.packet.length){switch(this._pos=0,this.packet.cmd){case"connect":this._parseConnect();break;case"connack":this._parseConnack();break;case"publish":this._parsePublish();break;case"puback":case"pubrec":case"pubrel":case"pubcomp":this._parseConfirmation();break;case"subscribe":this._parseSubscribe();break;case"suback":this._parseSuback();break;case"unsubscribe":this._parseUnsubscribe();break;case"unsuback":this._parseUnsuback();break;case"pingreq":case"pingresp":break;case"disconnect":this._parseDisconnect();break;case"auth":this._parseAuth();break;default:this._emitError(new Error("Not supported"))}e=!0}return D("_parsePayload complete result: %s",e),e}_parseConnect(){D("_parseConnect");let e,r,i,n,o={},s=this.packet,l=this._parseString();if(l===null)return this._emitError(new Error("Cannot parse protocolId"));if(l!=="MQTT"&&l!=="MQIsdp")return this._emitError(new Error("Invalid protocolId"));if(s.protocolId=l,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(s.protocolVersion=this._list.readUInt8(this._pos),s.protocolVersion>=128&&(s.bridgeMode=!0,s.protocolVersion=s.protocolVersion-128),s.protocolVersion!==3&&s.protocolVersion!==4&&s.protocolVersion!==5)return this._emitError(new Error("Invalid protocol version"));if(this._pos++,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(this._list.readUInt8(this._pos)&1)return this._emitError(new Error("Connect flag bit 0 must be 0, but got 1"));o.username=this._list.readUInt8(this._pos)&H.USERNAME_MASK,o.password=this._list.readUInt8(this._pos)&H.PASSWORD_MASK,o.will=this._list.readUInt8(this._pos)&H.WILL_FLAG_MASK;let u=!!(this._list.readUInt8(this._pos)&H.WILL_RETAIN_MASK),c=(this._list.readUInt8(this._pos)&H.WILL_QOS_MASK)>>H.WILL_QOS_SHIFT;if(o.will)s.will={},s.will.retain=u,s.will.qos=c;else{if(u)return this._emitError(new Error("Will Retain Flag must be set to zero when Will Flag is set to 0"));if(c)return this._emitError(new Error("Will QoS must be set to zero when Will Flag is set to 0"))}if(s.clean=(this._list.readUInt8(this._pos)&H.CLEAN_SESSION_MASK)!==0,this._pos++,s.keepalive=this._parseNum(),s.keepalive===-1)return this._emitError(new Error("Packet too short"));if(s.protocolVersion===5){let d=this._parseProperties();Object.getOwnPropertyNames(d).length&&(s.properties=d)}let h=this._parseString();if(h===null)return this._emitError(new Error("Packet too short"));if(s.clientId=h,D("_parseConnect: packet.clientId: %s",s.clientId),o.will){if(s.protocolVersion===5){let d=this._parseProperties();Object.getOwnPropertyNames(d).length&&(s.will.properties=d)}if(e=this._parseString(),e===null)return this._emitError(new Error("Cannot parse will topic"));if(s.will.topic=e,D("_parseConnect: packet.will.topic: %s",s.will.topic),r=this._parseBuffer(),r===null)return this._emitError(new Error("Cannot parse will payload"));s.will.payload=r,D("_parseConnect: packet.will.paylaod: %s",s.will.payload)}if(o.username){if(n=this._parseString(),n===null)return this._emitError(new Error("Cannot parse username"));s.username=n,D("_parseConnect: packet.username: %s",s.username)}if(o.password){if(i=this._parseBuffer(),i===null)return this._emitError(new Error("Cannot parse password"));s.password=i}return this.settings=s,D("_parseConnect: complete"),s}_parseConnack(){D("_parseConnack");let e=this.packet;if(this._list.length<1)return null;let r=this._list.readUInt8(this._pos++);if(r>1)return this._emitError(new Error("Invalid connack flags, bits 7-1 must be set to 0"));if(e.sessionPresent=!!(r&H.SESSIONPRESENT_MASK),this.settings.protocolVersion===5)this._list.length>=2?e.reasonCode=this._list.readUInt8(this._pos++):e.reasonCode=0;else{if(this._list.length<2)return null;e.returnCode=this._list.readUInt8(this._pos++)}if(e.returnCode===-1||e.reasonCode===-1)return this._emitError(new Error("Cannot parse return code"));if(this.settings.protocolVersion===5){let i=this._parseProperties();Object.getOwnPropertyNames(i).length&&(e.properties=i)}D("_parseConnack: complete")}_parsePublish(){D("_parsePublish");let e=this.packet;if(e.topic=this._parseString(),e.topic===null)return this._emitError(new Error("Cannot parse topic"));if(!(e.qos>0&&!this._parseMessageId())){if(this.settings.protocolVersion===5){let r=this._parseProperties();Object.getOwnPropertyNames(r).length&&(e.properties=r)}e.payload=this._list.slice(this._pos,e.length),D("_parsePublish: payload from buffer list: %o",e.payload)}}_parseSubscribe(){D("_parseSubscribe");let e=this.packet,r,i,n,o,s,l,u;if(e.subscriptions=[],!!this._parseMessageId()){if(this.settings.protocolVersion===5){let c=this._parseProperties();Object.getOwnPropertyNames(c).length&&(e.properties=c)}if(e.length<=0)return this._emitError(new Error("Malformed subscribe, no payload specified"));for(;this._pos<e.length;){if(r=this._parseString(),r===null)return this._emitError(new Error("Cannot parse topic"));if(this._pos>=e.length)return this._emitError(new Error("Malformed Subscribe Payload"));if(i=this._parseByte(),this.settings.protocolVersion===5){if(i&192)return this._emitError(new Error("Invalid subscribe topic flag bits, bits 7-6 must be 0"))}else if(i&252)return this._emitError(new Error("Invalid subscribe topic flag bits, bits 7-2 must be 0"));if(n=i&H.SUBSCRIBE_OPTIONS_QOS_MASK,n>2)return this._emitError(new Error("Invalid subscribe QoS, must be <= 2"));if(l=(i>>H.SUBSCRIBE_OPTIONS_NL_SHIFT&H.SUBSCRIBE_OPTIONS_NL_MASK)!==0,s=(i>>H.SUBSCRIBE_OPTIONS_RAP_SHIFT&H.SUBSCRIBE_OPTIONS_RAP_MASK)!==0,o=i>>H.SUBSCRIBE_OPTIONS_RH_SHIFT&H.SUBSCRIBE_OPTIONS_RH_MASK,o>2)return this._emitError(new Error("Invalid retain handling, must be <= 2"));u={topic:r,qos:n},this.settings.protocolVersion===5?(u.nl=l,u.rap=s,u.rh=o):this.settings.bridgeMode&&(u.rh=0,u.rap=!0,u.nl=!0),D("_parseSubscribe: push subscription `%s` to subscription",u),e.subscriptions.push(u)}}}_parseSuback(){D("_parseSuback");let e=this.packet;if(this.packet.granted=[],!!this._parseMessageId()){if(this.settings.protocolVersion===5){let r=this._parseProperties();Object.getOwnPropertyNames(r).length&&(e.properties=r)}if(e.length<=0)return this._emitError(new Error("Malformed suback, no payload specified"));for(;this._pos<this.packet.length;){let r=this._list.readUInt8(this._pos++);if(this.settings.protocolVersion===5){if(!H.MQTT5_SUBACK_CODES[r])return this._emitError(new Error("Invalid suback code"))}else if(r>2&&r!==128)return this._emitError(new Error("Invalid suback QoS, must be 0, 1, 2 or 128"));this.packet.granted.push(r)}}}_parseUnsubscribe(){D("_parseUnsubscribe");let e=this.packet;if(e.unsubscriptions=[],!!this._parseMessageId()){if(this.settings.protocolVersion===5){let r=this._parseProperties();Object.getOwnPropertyNames(r).length&&(e.properties=r)}if(e.length<=0)return this._emitError(new Error("Malformed unsubscribe, no payload specified"));for(;this._pos<e.length;){let r=this._parseString();if(r===null)return this._emitError(new Error("Cannot parse topic"));D("_parseUnsubscribe: push topic `%s` to unsubscriptions",r),e.unsubscriptions.push(r)}}}_parseUnsuback(){D("_parseUnsuback");let e=this.packet;if(!this._parseMessageId())return this._emitError(new Error("Cannot parse messageId"));if((this.settings.protocolVersion===3||this.settings.protocolVersion===4)&&e.length!==2)return this._emitError(new Error("Malformed unsuback, payload length must be 2"));if(e.length<=0)return this._emitError(new Error("Malformed unsuback, no payload specified"));if(this.settings.protocolVersion===5){let r=this._parseProperties();for(Object.getOwnPropertyNames(r).length&&(e.properties=r),e.granted=[];this._pos<this.packet.length;){let i=this._list.readUInt8(this._pos++);if(!H.MQTT5_UNSUBACK_CODES[i])return this._emitError(new Error("Invalid unsuback code"));this.packet.granted.push(i)}}}_parseConfirmation(){D("_parseConfirmation: packet.cmd: `%s`",this.packet.cmd);let e=this.packet;if(this._parseMessageId(),this.settings.protocolVersion===5){if(e.length>2){switch(e.reasonCode=this._parseByte(),this.packet.cmd){case"puback":case"pubrec":if(!H.MQTT5_PUBACK_PUBREC_CODES[e.reasonCode])return this._emitError(new Error("Invalid "+this.packet.cmd+" reason code"));break;case"pubrel":case"pubcomp":if(!H.MQTT5_PUBREL_PUBCOMP_CODES[e.reasonCode])return this._emitError(new Error("Invalid "+this.packet.cmd+" reason code"));break}D("_parseConfirmation: packet.reasonCode `%d`",e.reasonCode)}else e.reasonCode=0;if(e.length>3){let r=this._parseProperties();Object.getOwnPropertyNames(r).length&&(e.properties=r)}}return!0}_parseDisconnect(){let e=this.packet;if(D("_parseDisconnect"),this.settings.protocolVersion===5){this._list.length>0?(e.reasonCode=this._parseByte(),H.MQTT5_DISCONNECT_CODES[e.reasonCode]||this._emitError(new Error("Invalid disconnect reason code"))):e.reasonCode=0;let r=this._parseProperties();Object.getOwnPropertyNames(r).length&&(e.properties=r)}return D("_parseDisconnect result: true"),!0}_parseAuth(){D("_parseAuth");let e=this.packet;if(this.settings.protocolVersion!==5)return this._emitError(new Error("Not supported auth packet for this version MQTT"));if(e.reasonCode=this._parseByte(),!H.MQTT5_AUTH_CODES[e.reasonCode])return this._emitError(new Error("Invalid auth reason code"));let r=this._parseProperties();return Object.getOwnPropertyNames(r).length&&(e.properties=r),D("_parseAuth: result: true"),!0}_parseMessageId(){let e=this.packet;return e.messageId=this._parseNum(),e.messageId===null?(this._emitError(new Error("Cannot parse messageId")),!1):(D("_parseMessageId: packet.messageId %d",e.messageId),!0)}_parseString(e){let r=this._parseNum(),i=r+this._pos;if(r===-1||i>this._list.length||i>this.packet.length)return null;let n=this._list.toString("utf8",this._pos,i);return this._pos+=r,D("_parseString: result: %s",n),n}_parseStringPair(){return D("_parseStringPair"),{name:this._parseString(),value:this._parseString()}}_parseBuffer(){let e=this._parseNum(),r=e+this._pos;if(e===-1||r>this._list.length||r>this.packet.length)return null;let i=this._list.slice(this._pos,r);return this._pos+=e,D("_parseBuffer: result: %o",i),i}_parseNum(){if(this._list.length-this._pos<2)return-1;let e=this._list.readUInt16BE(this._pos);return this._pos+=2,D("_parseNum: result: %s",e),e}_parse4ByteNum(){if(this._list.length-this._pos<4)return-1;let e=this._list.readUInt32BE(this._pos);return this._pos+=4,D("_parse4ByteNum: result: %s",e),e}_parseVarByteNum(e){D("_parseVarByteNum");let r=4,i=0,n=1,o=0,s=!1,l,u=this._pos?this._pos:0;for(;i<r&&u+i<this._list.length;){if(l=this._list.readUInt8(u+i++),o+=n*(l&H.VARBYTEINT_MASK),n*=128,!(l&H.VARBYTEINT_FIN_MASK)){s=!0;break}if(this._list.length<=i)break}return!s&&i===r&&this._list.length>=i&&this._emitError(new Error("Invalid variable byte integer")),u&&(this._pos+=i),s?e?s={bytes:i,value:o}:s=o:s=!1,D("_parseVarByteNum: result: %o",s),s}_parseByte(){let e;return this._pos<this._list.length&&(e=this._list.readUInt8(this._pos),this._pos++),D("_parseByte: result: %o",e),e}_parseByType(e){switch(D("_parseByType: type: %s",e),e){case"byte":return this._parseByte()!==0;case"int8":return this._parseByte();case"int16":return this._parseNum();case"int32":return this._parse4ByteNum();case"var":return this._parseVarByteNum();case"string":return this._parseString();case"pair":return this._parseStringPair();case"binary":return this._parseBuffer()}}_parseProperties(){D("_parseProperties");let e=this._parseVarByteNum(),i=this._pos+e,n={};for(;this._pos<i;){let o=this._parseByte();if(!o)return this._emitError(new Error("Cannot parse property code type")),!1;let s=H.propertiesCodes[o];if(!s)return this._emitError(new Error("Unknown property")),!1;if(s==="userProperties"){n[s]||(n[s]=Object.create(null));let l=this._parseByType(H.propertiesTypes[s]);if(n[s][l.name])if(Array.isArray(n[s][l.name]))n[s][l.name].push(l.value);else{let u=n[s][l.name];n[s][l.name]=[u],n[s][l.name].push(l.value)}else n[s][l.name]=l.value;continue}n[s]?Array.isArray(n[s])?n[s].push(this._parseByType(H.propertiesTypes[s])):(n[s]=[n[s]],n[s].push(this._parseByType(H.propertiesTypes[s]))):n[s]=this._parseByType(H.propertiesTypes[s])}return n}_newPacket(){return D("_newPacket"),this.packet&&(this._list.consume(this.packet.length),D("_newPacket: parser emit packet: packet.cmd: %s, packet.payload: %s, packet.length: %d",this.packet.cmd,this.packet.payload,this.packet.length),this.emit("packet",this.packet)),D("_newPacket: new packet"),this.packet=new fd,this._pos=0,!0}_emitError(e){D("_emitError",e),this.error=e,this.emit("error",e)}};cd.exports=Lo});var yd=L((tC,gd)=>{_();E();m();var{Buffer:wi}=(ye(),Z(me)),fm=65536,dd={},cm=wi.isBuffer(wi.from([1,2]).subarray(0,1));function pd(t){let e=wi.allocUnsafe(2);return e.writeUInt8(t>>8,0),e.writeUInt8(t&255,0+1),e}function hm(){for(let t=0;t<fm;t++)dd[t]=pd(t)}function dm(t){let r=0,i=0,n=wi.allocUnsafe(4);do r=t%128|0,t=t/128|0,t>0&&(r=r|128),n.writeUInt8(r,i++);while(t>0&&i<4);return t>0&&(i=0),cm?n.subarray(0,i):n.slice(0,i)}function pm(t){let e=wi.allocUnsafe(4);return e.writeUInt32BE(t,0),e}gd.exports={cache:dd,generateCache:hm,generateNumber:pd,genBufVariableByteInt:dm,generate4ByteBuffer:pm}});var bd=L((aC,Uo)=>{"use strict";_();E();m();typeof C>"u"||!C.version||C.version.indexOf("v0.")===0||C.version.indexOf("v1.")===0&&C.version.indexOf("v1.8.")!==0?Uo.exports={nextTick:gm}:Uo.exports=C;function gm(t,e,r,i){if(typeof t!="function")throw new TypeError('"callback" argument must be a function');var n=arguments.length,o,s;switch(n){case 0:case 1:return C.nextTick(t);case 2:return C.nextTick(function(){t.call(null,e)});case 3:return C.nextTick(function(){t.call(null,e,r)});case 4:return C.nextTick(function(){t.call(null,e,r,i)});default:for(o=new Array(n-1),s=0;s<o.length;)o[s++]=arguments[s];return C.nextTick(function(){t.apply(null,o)})}}});var qo=L((gC,Id)=>{_();E();m();var j=ko(),{Buffer:q}=(ye(),Z(me)),ym=q.allocUnsafe(0),bm=q.from([0]),_i=yd(),wm=bd().nextTick,qe=ot()("mqtt-packet:writeToStream"),Sn=_i.cache,_m=_i.generateNumber,mm=_i.generateCache,Mo=_i.genBufVariableByteInt,Em=_i.generate4ByteBuffer,Ie=No,An=!0;function Sd(t,e,r){switch(qe("generate called"),e.cork&&(e.cork(),wm(vm,e)),An&&(An=!1,mm()),qe("generate: packet.cmd: %s",t.cmd),t.cmd){case"connect":return Sm(t,e,r);case"connack":return Am(t,e,r);case"publish":return Im(t,e,r);case"puback":case"pubrec":case"pubrel":case"pubcomp":return Tm(t,e,r);case"subscribe":return Rm(t,e,r);case"suback":return Cm(t,e,r);case"unsubscribe":return Bm(t,e,r);case"unsuback":return Pm(t,e,r);case"pingreq":case"pingresp":return xm(t,e,r);case"disconnect":return Om(t,e,r);case"auth":return km(t,e,r);default:return e.destroy(new Error("Unknown command")),!1}}Object.defineProperty(Sd,"cacheNumbers",{get(){return Ie===No},set(t){t?((!Sn||Object.keys(Sn).length===0)&&(An=!0),Ie=No):(An=!1,Ie=Lm)}});function vm(t){t.uncork()}function Sm(t,e,r){let i=t||{},n=i.protocolId||"MQTT",o=i.protocolVersion||4,s=i.will,l=i.clean,u=i.keepalive||0,c=i.clientId||"",h=i.username,d=i.password,g=i.properties;l===void 0&&(l=!0);let y=0;if(!n||typeof n!="string"&&!q.isBuffer(n))return e.destroy(new Error("Invalid protocolId")),!1;if(y+=n.length+2,o!==3&&o!==4&&o!==5)return e.destroy(new Error("Invalid protocol version")),!1;if(y+=1,(typeof c=="string"||q.isBuffer(c))&&(c||o>=4)&&(c||l))y+=q.byteLength(c)+2;else{if(o<4)return e.destroy(new Error("clientId must be supplied before 3.1.1")),!1;if(l*1===0)return e.destroy(new Error("clientId must be given if cleanSession set to 0")),!1}if(typeof u!="number"||u<0||u>65535||u%1!==0)return e.destroy(new Error("Invalid keepalive")),!1;y+=2,y+=1;let w,S;if(o===5){if(w=jt(e,g),!w)return!1;y+=w.length}if(s){if(typeof s!="object")return e.destroy(new Error("Invalid will")),!1;if(!s.topic||typeof s.topic!="string")return e.destroy(new Error("Invalid will topic")),!1;if(y+=q.byteLength(s.topic)+2,y+=2,s.payload)if(s.payload.length>=0)typeof s.payload=="string"?y+=q.byteLength(s.payload):y+=s.payload.length;else return e.destroy(new Error("Invalid will payload")),!1;if(S={},o===5){if(S=jt(e,s.properties),!S)return!1;y+=S.length}}let A=!1;if(h!=null)if(vd(h))A=!0,y+=q.byteLength(h)+2;else return e.destroy(new Error("Invalid username")),!1;if(d!=null){if(!A)return e.destroy(new Error("Username is required to use password")),!1;if(vd(d))y+=Ad(d)+2;else return e.destroy(new Error("Invalid password")),!1}e.write(j.CONNECT_HEADER),De(e,y),Hr(e,n),i.bridgeMode&&(o+=128),e.write(o===131?j.VERSION131:o===132?j.VERSION132:o===4?j.VERSION4:o===5?j.VERSION5:j.VERSION3);let I=0;return I|=h!=null?j.USERNAME_MASK:0,I|=d!=null?j.PASSWORD_MASK:0,I|=s&&s.retain?j.WILL_RETAIN_MASK:0,I|=s&&s.qos?s.qos<<j.WILL_QOS_SHIFT:0,I|=s?j.WILL_FLAG_MASK:0,I|=l?j.CLEAN_SESSION_MASK:0,e.write(q.from([I])),Ie(e,u),o===5&&w.write(),Hr(e,c),s&&(o===5&&S.write(),dr(e,s.topic),Hr(e,s.payload)),h!=null&&Hr(e,h),d!=null&&Hr(e,d),!0}function Am(t,e,r){let i=r?r.protocolVersion:4,n=t||{},o=i===5?n.reasonCode:n.returnCode,s=n.properties,l=2;if(typeof o!="number")return e.destroy(new Error("Invalid return code")),!1;let u=null;if(i===5){if(u=jt(e,s),!u)return!1;l+=u.length}return e.write(j.CONNACK_HEADER),De(e,l),e.write(n.sessionPresent?j.SESSIONPRESENT_HEADER:bm),e.write(q.from([o])),u?.write(),!0}function Im(t,e,r){qe("publish: packet: %o",t);let i=r?r.protocolVersion:4,n=t||{},o=n.qos||0,s=n.retain?j.RETAIN_MASK:0,l=n.topic,u=n.payload||ym,c=n.messageId,h=n.properties,d=0;if(typeof l=="string")d+=q.byteLength(l)+2;else if(q.isBuffer(l))d+=l.length+2;else return e.destroy(new Error("Invalid topic")),!1;if(q.isBuffer(u)?d+=u.length:d+=q.byteLength(u),o&&typeof c!="number")return e.destroy(new Error("Invalid messageId")),!1;o&&(d+=2);let g=null;if(i===5){if(g=jt(e,h),!g)return!1;d+=g.length}return e.write(j.PUBLISH_HEADER[o][n.dup?1:0][s?1:0]),De(e,d),Ie(e,Ad(l)),e.write(l),o>0&&Ie(e,c),g?.write(),qe("publish: payload: %o",u),e.write(u)}function Tm(t,e,r){let i=r?r.protocolVersion:4,n=t||{},o=n.cmd||"puback",s=n.messageId,l=n.dup&&o==="pubrel"?j.DUP_MASK:0,u=0,c=n.reasonCode,h=n.properties,d=i===5?3:2;if(o==="pubrel"&&(u=1),typeof s!="number")return e.destroy(new Error("Invalid messageId")),!1;let g=null;if(i===5&&typeof h=="object"){if(g=mi(e,h,r,d),!g)return!1;d+=g.length}return e.write(j.ACKS[o][u][l][0]),d===3&&(d+=c!==0?1:-1),De(e,d),Ie(e,s),i===5&&d!==2&&e.write(q.from([c])),g!==null?g.write():d===4&&e.write(q.from([0])),!0}function Rm(t,e,r){qe("subscribe: packet: ");let i=r?r.protocolVersion:4,n=t||{},o=n.dup?j.DUP_MASK:0,s=n.messageId,l=n.subscriptions,u=n.properties,c=0;if(typeof s!="number")return e.destroy(new Error("Invalid messageId")),!1;c+=2;let h=null;if(i===5){if(h=jt(e,u),!h)return!1;c+=h.length}if(typeof l=="object"&&l.length)for(let g=0;g<l.length;g+=1){let y=l[g].topic,w=l[g].qos;if(typeof y!="string")return e.destroy(new Error("Invalid subscriptions - invalid topic")),!1;if(typeof w!="number")return e.destroy(new Error("Invalid subscriptions - invalid qos")),!1;if(i===5){if(typeof(l[g].nl||!1)!="boolean")return e.destroy(new Error("Invalid subscriptions - invalid No Local")),!1;if(typeof(l[g].rap||!1)!="boolean")return e.destroy(new Error("Invalid subscriptions - invalid Retain as Published")),!1;let I=l[g].rh||0;if(typeof I!="number"||I>2)return e.destroy(new Error("Invalid subscriptions - invalid Retain Handling")),!1}c+=q.byteLength(y)+2+1}else return e.destroy(new Error("Invalid subscriptions")),!1;qe("subscribe: writing to stream: %o",j.SUBSCRIBE_HEADER),e.write(j.SUBSCRIBE_HEADER[1][o?1:0][0]),De(e,c),Ie(e,s),h!==null&&h.write();let d=!0;for(let g of l){let y=g.topic,w=g.qos,S=+g.nl,A=+g.rap,I=g.rh,P;dr(e,y),P=j.SUBSCRIBE_OPTIONS_QOS[w],i===5&&(P|=S?j.SUBSCRIBE_OPTIONS_NL:0,P|=A?j.SUBSCRIBE_OPTIONS_RAP:0,P|=I?j.SUBSCRIBE_OPTIONS_RH[I]:0),d=e.write(q.from([P]))}return d}function Cm(t,e,r){let i=r?r.protocolVersion:4,n=t||{},o=n.messageId,s=n.granted,l=n.properties,u=0;if(typeof o!="number")return e.destroy(new Error("Invalid messageId")),!1;if(u+=2,typeof s=="object"&&s.length)for(let h=0;h<s.length;h+=1){if(typeof s[h]!="number")return e.destroy(new Error("Invalid qos vector")),!1;u+=1}else return e.destroy(new Error("Invalid qos vector")),!1;let c=null;if(i===5){if(c=mi(e,l,r,u),!c)return!1;u+=c.length}return e.write(j.SUBACK_HEADER),De(e,u),Ie(e,o),c!==null&&c.write(),e.write(q.from(s))}function Bm(t,e,r){let i=r?r.protocolVersion:4,n=t||{},o=n.messageId,s=n.dup?j.DUP_MASK:0,l=n.unsubscriptions,u=n.properties,c=0;if(typeof o!="number")return e.destroy(new Error("Invalid messageId")),!1;if(c+=2,typeof l=="object"&&l.length)for(let g=0;g<l.length;g+=1){if(typeof l[g]!="string")return e.destroy(new Error("Invalid unsubscriptions")),!1;c+=q.byteLength(l[g])+2}else return e.destroy(new Error("Invalid unsubscriptions")),!1;let h=null;if(i===5){if(h=jt(e,u),!h)return!1;c+=h.length}e.write(j.UNSUBSCRIBE_HEADER[1][s?1:0][0]),De(e,c),Ie(e,o),h!==null&&h.write();let d=!0;for(let g=0;g<l.length;g++)d=dr(e,l[g]);return d}function Pm(t,e,r){let i=r?r.protocolVersion:4,n=t||{},o=n.messageId,s=n.dup?j.DUP_MASK:0,l=n.granted,u=n.properties,c=n.cmd,h=0,d=2;if(typeof o!="number")return e.destroy(new Error("Invalid messageId")),!1;if(i===5)if(typeof l=="object"&&l.length)for(let y=0;y<l.length;y+=1){if(typeof l[y]!="number")return e.destroy(new Error("Invalid qos vector")),!1;d+=1}else return e.destroy(new Error("Invalid qos vector")),!1;let g=null;if(i===5){if(g=mi(e,u,r,d),!g)return!1;d+=g.length}return e.write(j.ACKS[c][h][s][0]),De(e,d),Ie(e,o),g!==null&&g.write(),i===5&&e.write(q.from(l)),!0}function xm(t,e,r){return e.write(j.EMPTY[t.cmd])}function Om(t,e,r){let i=r?r.protocolVersion:4,n=t||{},o=n.reasonCode,s=n.properties,l=i===5?1:0,u=null;if(i===5){if(u=mi(e,s,r,l),!u)return!1;l+=u.length}return e.write(q.from([j.codes.disconnect<<4])),De(e,l),i===5&&e.write(q.from([o])),u!==null&&u.write(),!0}function km(t,e,r){let i=r?r.protocolVersion:4,n=t||{},o=n.reasonCode,s=n.properties,l=i===5?1:0;i!==5&&e.destroy(new Error("Invalid mqtt version for auth packet"));let u=mi(e,s,r,l);return u?(l+=u.length,e.write(q.from([j.codes.auth<<4])),De(e,l),e.write(q.from([o])),u!==null&&u.write(),!0):!1}var wd={};function De(t,e){if(e>j.VARBYTEINT_MAX)return t.destroy(new Error(`Invalid variable byte integer: ${e}`)),!1;let r=wd[e];return r||(r=Mo(e),e<16384&&(wd[e]=r)),qe("writeVarByteInt: writing to stream: %o",r),t.write(r)}function dr(t,e){let r=q.byteLength(e);return Ie(t,r),qe("writeString: %s",e),t.write(e,"utf8")}function _d(t,e,r){dr(t,e),dr(t,r)}function No(t,e){return qe("writeNumberCached: number: %d",e),qe("writeNumberCached: %o",Sn[e]),t.write(Sn[e])}function Lm(t,e){let r=_m(e);return qe("writeNumberGenerated: %o",r),t.write(r)}function Um(t,e){let r=Em(e);return qe("write4ByteNumber: %o",r),t.write(r)}function Hr(t,e){typeof e=="string"?dr(t,e):e?(Ie(t,e.length),t.write(e)):Ie(t,0)}function jt(t,e){if(typeof e!="object"||e.length!=null)return{length:1,write(){Ed(t,{},0)}};let r=0;function i(o,s){let l=j.propertiesTypes[o],u=0;switch(l){case"byte":{if(typeof s!="boolean")return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=1+1;break}case"int8":{if(typeof s!="number"||s<0||s>255)return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=1+1;break}case"binary":{if(s&&s===null)return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=1+q.byteLength(s)+2;break}case"int16":{if(typeof s!="number"||s<0||s>65535)return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=1+2;break}case"int32":{if(typeof s!="number"||s<0||s>4294967295)return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=1+4;break}case"var":{if(typeof s!="number"||s<0||s>268435455)return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=1+q.byteLength(Mo(s));break}case"string":{if(typeof s!="string")return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=1+2+q.byteLength(s.toString());break}case"pair":{if(typeof s!="object")return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=Object.getOwnPropertyNames(s).reduce((c,h)=>{let d=s[h];return Array.isArray(d)?c+=d.reduce((g,y)=>(g+=1+2+q.byteLength(h.toString())+2+q.byteLength(y.toString()),g),0):c+=1+2+q.byteLength(h.toString())+2+q.byteLength(s[h].toString()),c},0);break}default:return t.destroy(new Error(`Invalid property ${o}: ${s}`)),!1}return u}if(e)for(let o in e){let s=0,l=0,u=e[o];if(Array.isArray(u))for(let c=0;c<u.length;c++){if(l=i(o,u[c]),!l)return!1;s+=l}else{if(l=i(o,u),!l)return!1;s=l}if(!s)return!1;r+=s}return{length:q.byteLength(Mo(r))+r,write(){Ed(t,e,r)}}}function mi(t,e,r,i){let n=["reasonString","userProperties"],o=r&&r.properties&&r.properties.maximumPacketSize?r.properties.maximumPacketSize:0,s=jt(t,e);if(o)for(;i+s.length>o;){let l=n.shift();if(l&&e[l])delete e[l],s=jt(t,e);else return!1}return s}function md(t,e,r){switch(j.propertiesTypes[e]){case"byte":{t.write(q.from([j.properties[e]])),t.write(q.from([+r]));break}case"int8":{t.write(q.from([j.properties[e]])),t.write(q.from([r]));break}case"binary":{t.write(q.from([j.properties[e]])),Hr(t,r);break}case"int16":{t.write(q.from([j.properties[e]])),Ie(t,r);break}case"int32":{t.write(q.from([j.properties[e]])),Um(t,r);break}case"var":{t.write(q.from([j.properties[e]])),De(t,r);break}case"string":{t.write(q.from([j.properties[e]])),dr(t,r);break}case"pair":{Object.getOwnPropertyNames(r).forEach(n=>{let o=r[n];Array.isArray(o)?o.forEach(s=>{t.write(q.from([j.properties[e]])),_d(t,n.toString(),s.toString())}):(t.write(q.from([j.properties[e]])),_d(t,n.toString(),o.toString()))});break}default:return t.destroy(new Error(`Invalid property ${e} value: ${r}`)),!1}}function Ed(t,e,r){De(t,r);for(let i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&e[i]!==null){let n=e[i];if(Array.isArray(n))for(let o=0;o<n.length;o++)md(t,i,n[o]);else md(t,i,n)}}function Ad(t){return t?t instanceof q?t.length:q.byteLength(t):0}function vd(t){return typeof t=="string"||t instanceof q}Id.exports=Sd});var Cd=L((vC,Rd)=>{_();E();m();var Mm=qo(),{EventEmitter:Nm}=(er(),Z(Zt)),{Buffer:Td}=(ye(),Z(me));function qm(t,e){let r=new Do;return Mm(t,r,e),r.concat()}var Do=class extends Nm{constructor(){super(),this._array=new Array(20),this._i=0}write(e){return this._array[this._i++]=e,!0}concat(){let e=0,r=new Array(this._array.length),i=this._array,n=0,o;for(o=0;o<i.length&&i[o]!==void 0;o++)typeof i[o]!="string"?r[o]=i[o].length:r[o]=Td.byteLength(i[o]),e+=r[o];let s=Td.allocUnsafe(e);for(o=0;o<i.length&&i[o]!==void 0;o++)typeof i[o]!="string"?(i[o].copy(s,n),n+=r[o]):(s.write(i[o],n),n+=r[o]);return s}destroy(e){e&&this.emit("error",e)}};Rd.exports=qm});var Bd=L(In=>{_();E();m();In.parser=hd().parser;In.generate=Cd();In.writeToStream=qo()});var Wo=L(Fo=>{"use strict";_();E();m();Object.defineProperty(Fo,"__esModule",{value:!0});var jo=class{constructor(){this.nextId=Math.max(1,Math.floor(Math.random()*65535))}allocate(){let e=this.nextId++;return this.nextId===65536&&(this.nextId=1),e}getLastAllocated(){return this.nextId===1?65535:this.nextId-1}register(e){return!0}deallocate(e){}clear(){}};Fo.default=jo});var xd=L(($C,Pd)=>{"use strict";_();E();m();function Dm(t,e,r){var i=this;this._callback=t,this._args=r,this._interval=setInterval(t,e,this._args),this.reschedule=function(n){n||(n=i._interval),i._interval&&clearInterval(i._interval),i._interval=setInterval(i._callback,n,i._args)},this.clear=function(){i._interval&&(clearInterval(i._interval),i._interval=void 0)},this.destroy=function(){i._interval&&clearInterval(i._interval),i._callback=void 0,i._interval=void 0,i._args=void 0}}function jm(){if(typeof arguments[0]!="function")throw new Error("callback needed");if(typeof arguments[1]!="number")throw new Error("interval needed");var t;if(arguments.length>0){t=new Array(arguments.length-2);for(var e=0;e<t.length;e++)t[e]=arguments[e+2]}return new Dm(arguments[0],arguments[1],t)}Pd.exports=jm});var kd=L((YC,Od)=>{"use strict";_();E();m();Od.exports=Fm;function Vr(t){return t instanceof k?k.from(t):new t.constructor(t.buffer.slice(),t.byteOffset,t.length)}function Fm(t){if(t=t||{},t.circles)return Wm(t);return t.proto?i:r;function e(n,o){for(var s=Object.keys(n),l=new Array(s.length),u=0;u<s.length;u++){var c=s[u],h=n[c];typeof h!="object"||h===null?l[c]=h:h instanceof Date?l[c]=new Date(h):ArrayBuffer.isView(h)?l[c]=Vr(h):l[c]=o(h)}return l}function r(n){if(typeof n!="object"||n===null)return n;if(n instanceof Date)return new Date(n);if(Array.isArray(n))return e(n,r);if(n instanceof Map)return new Map(e(Array.from(n),r));if(n instanceof Set)return new Set(e(Array.from(n),r));var o={};for(var s in n)if(Object.hasOwnProperty.call(n,s)!==!1){var l=n[s];typeof l!="object"||l===null?o[s]=l:l instanceof Date?o[s]=new Date(l):l instanceof Map?o[s]=new Map(e(Array.from(l),r)):l instanceof Set?o[s]=new Set(e(Array.from(l),r)):ArrayBuffer.isView(l)?o[s]=Vr(l):o[s]=r(l)}return o}function i(n){if(typeof n!="object"||n===null)return n;if(n instanceof Date)return new Date(n);if(Array.isArray(n))return e(n,i);if(n instanceof Map)return new Map(e(Array.from(n),i));if(n instanceof Set)return new Set(e(Array.from(n),i));var o={};for(var s in n){var l=n[s];typeof l!="object"||l===null?o[s]=l:l instanceof Date?o[s]=new Date(l):l instanceof Map?o[s]=new Map(e(Array.from(l),i)):l instanceof Set?o[s]=new Set(e(Array.from(l),i)):ArrayBuffer.isView(l)?o[s]=Vr(l):o[s]=i(l)}return o}}function Wm(t){var e=[],r=[];return t.proto?o:n;function i(s,l){for(var u=Object.keys(s),c=new Array(u.length),h=0;h<u.length;h++){var d=u[h],g=s[d];if(typeof g!="object"||g===null)c[d]=g;else if(g instanceof Date)c[d]=new Date(g);else if(ArrayBuffer.isView(g))c[d]=Vr(g);else{var y=e.indexOf(g);y!==-1?c[d]=r[y]:c[d]=l(g)}}return c}function n(s){if(typeof s!="object"||s===null)return s;if(s instanceof Date)return new Date(s);if(Array.isArray(s))return i(s,n);if(s instanceof Map)return new Map(i(Array.from(s),n));if(s instanceof Set)return new Set(i(Array.from(s),n));var l={};e.push(s),r.push(l);for(var u in s)if(Object.hasOwnProperty.call(s,u)!==!1){var c=s[u];if(typeof c!="object"||c===null)l[u]=c;else if(c instanceof Date)l[u]=new Date(c);else if(c instanceof Map)l[u]=new Map(i(Array.from(c),n));else if(c instanceof Set)l[u]=new Set(i(Array.from(c),n));else if(ArrayBuffer.isView(c))l[u]=Vr(c);else{var h=e.indexOf(c);h!==-1?l[u]=r[h]:l[u]=n(c)}}return e.pop(),r.pop(),l}function o(s){if(typeof s!="object"||s===null)return s;if(s instanceof Date)return new Date(s);if(Array.isArray(s))return i(s,o);if(s instanceof Map)return new Map(i(Array.from(s),o));if(s instanceof Set)return new Set(i(Array.from(s),o));var l={};e.push(s),r.push(l);for(var u in s){var c=s[u];if(typeof c!="object"||c===null)l[u]=c;else if(c instanceof Date)l[u]=new Date(c);else if(c instanceof Map)l[u]=new Map(i(Array.from(c),o));else if(c instanceof Set)l[u]=new Set(i(Array.from(c),o));else if(ArrayBuffer.isView(c))l[u]=Vr(c);else{var h=e.indexOf(c);h!==-1?l[u]=r[h]:l[u]=o(c)}}return e.pop(),r.pop(),l}}});var Ud=L((iB,Ld)=>{"use strict";_();E();m();Ld.exports=kd()()});var Nd=L(zr=>{"use strict";_();E();m();Object.defineProperty(zr,"__esModule",{value:!0});zr.validateTopics=zr.validateTopic=void 0;function Md(t){let e=t.split("/");for(let r=0;r<e.length;r++)if(e[r]!=="+"){if(e[r]==="#")return r===e.length-1;if(e[r].indexOf("+")!==-1||e[r].indexOf("#")!==-1)return!1}return!0}zr.validateTopic=Md;function $m(t){if(t.length===0)return"empty_topic_list";for(let e=0;e<t.length;e++)if(!Md(t[e]))return t[e];return null}zr.validateTopics=$m});var Vo=L(Ho=>{"use strict";_();E();m();Object.defineProperty(Ho,"__esModule",{value:!0});var Hm=qt(),Vm={objectMode:!0},zm={clean:!0},$o=class{constructor(e){this.options=e||{},this.options=Object.assign(Object.assign({},zm),e),this._inflights=new Map}put(e,r){return this._inflights.set(e.messageId,e),r&&r(),this}createStream(){let e=new Hm.Readable(Vm),r=[],i=!1,n=0;return this._inflights.forEach((o,s)=>{r.push(o)}),e._read=()=>{!i&&n<r.length?e.push(r[n++]):e.push(null)},e.destroy=o=>{if(!i)return i=!0,setTimeout(()=>{e.emit("close")},0),e},e}del(e,r){let i=this._inflights.get(e.messageId);return i?(this._inflights.delete(e.messageId),r(null,i)):r&&r(new Error("missing packet")),this}get(e,r){let i=this._inflights.get(e.messageId);return i?r(null,i):r&&r(new Error("missing packet")),this}close(e){this.options.clean&&(this._inflights=null),e&&e()}};Ho.default=$o});var Dd=L(zo=>{"use strict";_();E();m();Object.defineProperty(zo,"__esModule",{value:!0});var qd=[0,16,128,131,135,144,145,151,153],Km=(t,e,r)=>{t.log("handlePublish: packet %o",e),r=typeof r<"u"?r:t.noop;let i=e.topic.toString(),n=e.payload,{qos:o}=e,{messageId:s}=e,{options:l}=t;if(t.options.protocolVersion===5){let u;if(e.properties&&(u=e.properties.topicAlias),typeof u<"u")if(i.length===0)if(u>0&&u<=65535){let c=t.topicAliasRecv.getTopicByAlias(u);if(c)i=c,t.log("handlePublish :: topic complemented by alias. topic: %s - alias: %d",i,u);else{t.log("handlePublish :: unregistered topic alias. alias: %d",u),t.emit("error",new Error("Received unregistered Topic Alias"));return}}else{t.log("handlePublish :: topic alias out of range. alias: %d",u),t.emit("error",new Error("Received Topic Alias is out of range"));return}else if(t.topicAliasRecv.put(i,u))t.log("handlePublish :: registered topic: %s - alias: %d",i,u);else{t.log("handlePublish :: topic alias out of range. alias: %d",u),t.emit("error",new Error("Received Topic Alias is out of range"));return}}switch(t.log("handlePublish: qos %d",o),o){case 2:{l.customHandleAcks(i,n,e,(u,c)=>{if(typeof u=="number"&&(c=u,u=null),u)return t.emit("error",u);if(qd.indexOf(c)===-1)return t.emit("error",new Error("Wrong reason code for pubrec"));c?t._sendPacket({cmd:"pubrec",messageId:s,reasonCode:c},r):t.incomingStore.put(e,()=>{t._sendPacket({cmd:"pubrec",messageId:s},r)})});break}case 1:{l.customHandleAcks(i,n,e,(u,c)=>{if(typeof u=="number"&&(c=u,u=null),u)return t.emit("error",u);if(qd.indexOf(c)===-1)return t.emit("error",new Error("Wrong reason code for puback"));c||t.emit("message",i,n,e),t.handleMessage(e,h=>{if(h)return r&&r(h);t._sendPacket({cmd:"puback",messageId:s,reasonCode:c},r)})});break}case 0:t.emit("message",i,n,e),t.handleMessage(e,r);break;default:t.log("handlePublish: unknown QoS. Doing nothing.");break}};zo.default=Km});var Ei=L(Kr=>{"use strict";_();E();m();Object.defineProperty(Kr,"__esModule",{value:!0});Kr.applyMixin=Kr.ErrorWithReasonCode=void 0;var Ko=class t extends Error{constructor(e,r){super(e),this.code=r,Object.setPrototypeOf(this,t.prototype),Object.getPrototypeOf(this).name="ErrorWithReasonCode"}};Kr.ErrorWithReasonCode=Ko;function Qm(t,e,r=!1){var i;let n=[e];for(;;){let o=n[0],s=Object.getPrototypeOf(o);if(s?.prototype)n.unshift(s);else break}for(let o of n)for(let s of Object.getOwnPropertyNames(o.prototype))(r||s!=="constructor")&&Object.defineProperty(t.prototype,s,(i=Object.getOwnPropertyDescriptor(o.prototype,s))!==null&&i!==void 0?i:Object.create(null))}Kr.applyMixin=Qm});var vi=L(pr=>{"use strict";_();E();m();Object.defineProperty(pr,"__esModule",{value:!0});pr.ReasonCodes=void 0;pr.ReasonCodes={0:"",1:"Unacceptable protocol version",2:"Identifier rejected",3:"Server unavailable",4:"Bad username or password",5:"Not authorized",16:"No matching subscribers",17:"No subscription existed",128:"Unspecified error",129:"Malformed Packet",130:"Protocol Error",131:"Implementation specific error",132:"Unsupported Protocol Version",133:"Client Identifier not valid",134:"Bad User Name or Password",135:"Not authorized",136:"Server unavailable",137:"Server busy",138:"Banned",139:"Server shutting down",140:"Bad authentication method",141:"Keep Alive timeout",142:"Session taken over",143:"Topic Filter invalid",144:"Topic Name invalid",145:"Packet identifier in use",146:"Packet Identifier not found",147:"Receive Maximum exceeded",148:"Topic Alias invalid",149:"Packet too large",150:"Message rate too high",151:"Quota exceeded",152:"Administrative action",153:"Payload format invalid",154:"Retain not supported",155:"QoS not supported",156:"Use another server",157:"Server moved",158:"Shared Subscriptions not supported",159:"Connection rate exceeded",160:"Maximum connect time",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"};var Gm=(t,e)=>{let{messageId:r}=e,i=e.cmd,n=null,o=t.outgoing[r]?t.outgoing[r].cb:null,s;if(!o){t.log("_handleAck :: Server sent an ack in error. Ignoring.");return}switch(t.log("_handleAck :: packet type",i),i){case"pubcomp":case"puback":{let l=e.reasonCode;l&&l>0&&l!==16?(s=new Error(`Publish error: ${pr.ReasonCodes[l]}`),s.code=l,t._removeOutgoingAndStoreMessage(r,()=>{o(s,e)})):t._removeOutgoingAndStoreMessage(r,o);break}case"pubrec":{n={cmd:"pubrel",qos:2,messageId:r};let l=e.reasonCode;l&&l>0&&l!==16?(s=new Error(`Publish error: ${pr.ReasonCodes[l]}`),s.code=l,t._removeOutgoingAndStoreMessage(r,()=>{o(s,e)})):t._sendPacket(n);break}case"suback":{delete t.outgoing[r],t.messageIdProvider.deallocate(r);let l=e.granted;for(let u=0;u<l.length;u++)if(l[u]&128){let c=t.messageIdToTopic[r];c&&c.forEach(h=>{delete t._resubscribeTopics[h]})}delete t.messageIdToTopic[r],t._invokeStoreProcessingQueue(),o(null,e);break}case"unsuback":{delete t.outgoing[r],t.messageIdProvider.deallocate(r),t._invokeStoreProcessingQueue(),o(null);break}default:t.emit("error",new Error("unrecognized packet type"))}t.disconnecting&&Object.keys(t.outgoing).length===0&&t.emit("outgoingEmpty")};pr.default=Gm});var Fd=L(Qo=>{"use strict";_();E();m();Object.defineProperty(Qo,"__esModule",{value:!0});var jd=Ei(),Ym=vi(),Jm=(t,e)=>{let{options:r}=t,i=r.protocolVersion,n=i===5?e.reasonCode:e.returnCode;if(i!==5){let o=new jd.ErrorWithReasonCode(`Protocol error: Auth packets are only supported in MQTT 5. Your version:${i}`,n);t.emit("error",o);return}t.handleAuth(e,(o,s)=>{if(o){t.emit("error",o);return}if(n===24)t.reconnecting=!1,t._sendPacket(s);else{let l=new jd.ErrorWithReasonCode(`Connection refused: ${Ym.ReasonCodes[n]}`,n);t.emit("error",l)}})};Qo.default=Jm});var zd=L(Rn=>{"use strict";_();E();m();Object.defineProperty(Rn,"__esModule",{value:!0});Rn.LRUCache=void 0;var Si=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,$d=new Set,Go=typeof C=="object"&&C?C:{},Hd=(t,e,r,i)=>{typeof Go.emitWarning=="function"?Go.emitWarning(t,e,r,i):console.error(`[${r}] ${e}: ${t}`)},Tn=globalThis.AbortController,Wd=globalThis.AbortSignal;if(typeof Tn>"u"){Wd=class{onabort;_onabort=[];reason;aborted=!1;addEventListener(i,n){this._onabort.push(n)}},Tn=class{constructor(){e()}signal=new Wd;abort(i){if(!this.signal.aborted){this.signal.reason=i,this.signal.aborted=!0;for(let n of this.signal._onabort)n(i);this.signal.onabort?.(i)}}};let t=Go.env?.LRU_CACHE_IGNORE_AC_WARNING!=="1",e=()=>{t&&(t=!1,Hd("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",e))}}var Xm=t=>!$d.has(t),eP=Symbol("type"),Ft=t=>t&&t===Math.floor(t)&&t>0&&isFinite(t),Vd=t=>Ft(t)?t<=Math.pow(2,8)?Uint8Array:t<=Math.pow(2,16)?Uint16Array:t<=Math.pow(2,32)?Uint32Array:t<=Number.MAX_SAFE_INTEGER?Qr:null:null,Qr=class extends Array{constructor(e){super(e),this.fill(0)}},Yo=class t{heap;length;static#a=!1;static create(e){let r=Vd(e);if(!r)return[];t.#a=!0;let i=new t(e,r);return t.#a=!1,i}constructor(e,r){if(!t.#a)throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new r(e),this.length=0}push(e){this.heap[this.length++]=e}pop(){return this.heap[--this.length]}},Jo=class t{#a;#c;#p;#g;#B;ttl;ttlResolution;ttlAutopurge;updateAgeOnGet;updateAgeOnHas;allowStale;noDisposeOnSet;noUpdateTTL;maxEntrySize;sizeCalculation;noDeleteOnFetchRejection;noDeleteOnStaleGet;allowStaleOnFetchAbort;allowStaleOnFetchRejection;ignoreFetchAbort;#i;#y;#n;#r;#e;#u;#h;#l;#s;#b;#o;#v;#S;#w;#_;#I;#f;static unsafeExposeInternals(e){return{starts:e.#S,ttls:e.#w,sizes:e.#v,keyMap:e.#n,keyList:e.#r,valList:e.#e,next:e.#u,prev:e.#h,get head(){return e.#l},get tail(){return e.#s},free:e.#b,isBackgroundFetch:r=>e.#t(r),backgroundFetch:(r,i,n,o)=>e.#O(r,i,n,o),moveToTail:r=>e.#C(r),indexes:r=>e.#m(r),rindexes:r=>e.#E(r),isStale:r=>e.#d(r)}}get max(){return this.#a}get maxSize(){return this.#c}get calculatedSize(){return this.#y}get size(){return this.#i}get fetchMethod(){return this.#B}get dispose(){return this.#p}get disposeAfter(){return this.#g}constructor(e){let{max:r=0,ttl:i,ttlResolution:n=1,ttlAutopurge:o,updateAgeOnGet:s,updateAgeOnHas:l,allowStale:u,dispose:c,disposeAfter:h,noDisposeOnSet:d,noUpdateTTL:g,maxSize:y=0,maxEntrySize:w=0,sizeCalculation:S,fetchMethod:A,noDeleteOnFetchRejection:I,noDeleteOnStaleGet:P,allowStaleOnFetchRejection:R,allowStaleOnFetchAbort:M,ignoreFetchAbort:N}=e;if(r!==0&&!Ft(r))throw new TypeError("max option must be a nonnegative integer");let V=r?Vd(r):Array;if(!V)throw new Error("invalid max value: "+r);if(this.#a=r,this.#c=y,this.maxEntrySize=w||this.#c,this.sizeCalculation=S,this.sizeCalculation){if(!this.#c&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(A!==void 0&&typeof A!="function")throw new TypeError("fetchMethod must be a function if specified");if(this.#B=A,this.#I=!!A,this.#n=new Map,this.#r=new Array(r).fill(void 0),this.#e=new Array(r).fill(void 0),this.#u=new V(r),this.#h=new V(r),this.#l=0,this.#s=0,this.#b=Yo.create(r),this.#i=0,this.#y=0,typeof c=="function"&&(this.#p=c),typeof h=="function"?(this.#g=h,this.#o=[]):(this.#g=void 0,this.#o=void 0),this.#_=!!this.#p,this.#f=!!this.#g,this.noDisposeOnSet=!!d,this.noUpdateTTL=!!g,this.noDeleteOnFetchRejection=!!I,this.allowStaleOnFetchRejection=!!R,this.allowStaleOnFetchAbort=!!M,this.ignoreFetchAbort=!!N,this.maxEntrySize!==0){if(this.#c!==0&&!Ft(this.#c))throw new TypeError("maxSize must be a positive integer if specified");if(!Ft(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");this.#q()}if(this.allowStale=!!u,this.noDeleteOnStaleGet=!!P,this.updateAgeOnGet=!!s,this.updateAgeOnHas=!!l,this.ttlResolution=Ft(n)||n===0?n:1,this.ttlAutopurge=!!o,this.ttl=i||0,this.ttl){if(!Ft(this.ttl))throw new TypeError("ttl must be a positive integer if specified");this.#k()}if(this.#a===0&&this.ttl===0&&this.#c===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!this.#a&&!this.#c){let Q="LRU_CACHE_UNBOUNDED";Xm(Q)&&($d.add(Q),Hd("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",Q,t))}}getRemainingTTL(e){return this.#n.has(e)?1/0:0}#k(){let e=new Qr(this.#a),r=new Qr(this.#a);this.#w=e,this.#S=r,this.#L=(o,s,l=Si.now())=>{if(r[o]=s!==0?l:0,e[o]=s,s!==0&&this.ttlAutopurge){let u=setTimeout(()=>{this.#d(o)&&this.delete(this.#r[o])},s+1);u.unref&&u.unref()}},this.#T=o=>{r[o]=e[o]!==0?Si.now():0},this.#A=(o,s)=>{if(e[s]){let l=e[s],u=r[s];o.ttl=l,o.start=u,o.now=i||n();let c=o.now-u;o.remainingTTL=l-c}};let i=0,n=()=>{let o=Si.now();if(this.ttlResolution>0){i=o;let s=setTimeout(()=>i=0,this.ttlResolution);s.unref&&s.unref()}return o};this.getRemainingTTL=o=>{let s=this.#n.get(o);if(s===void 0)return 0;let l=e[s],u=r[s];if(l===0||u===0)return 1/0;let c=(i||n())-u;return l-c},this.#d=o=>e[o]!==0&&r[o]!==0&&(i||n())-r[o]>e[o]}#T=()=>{};#A=()=>{};#L=()=>{};#d=()=>!1;#q(){let e=new Qr(this.#a);this.#y=0,this.#v=e,this.#R=r=>{this.#y-=e[r],e[r]=0},this.#U=(r,i,n,o)=>{if(this.#t(i))return 0;if(!Ft(n))if(o){if(typeof o!="function")throw new TypeError("sizeCalculation must be a function");if(n=o(i,r),!Ft(n))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}else throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");return n},this.#P=(r,i,n)=>{if(e[r]=i,this.#c){let o=this.#c-e[r];for(;this.#y>o;)this.#x(!0)}this.#y+=e[r],n&&(n.entrySize=i,n.totalCalculatedSize=this.#y)}}#R=e=>{};#P=(e,r,i)=>{};#U=(e,r,i,n)=>{if(i||n)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0};*#m({allowStale:e=this.allowStale}={}){if(this.#i)for(let r=this.#s;!(!this.#M(r)||((e||!this.#d(r))&&(yield r),r===this.#l));)r=this.#h[r]}*#E({allowStale:e=this.allowStale}={}){if(this.#i)for(let r=this.#l;!(!this.#M(r)||((e||!this.#d(r))&&(yield r),r===this.#s));)r=this.#u[r]}#M(e){return e!==void 0&&this.#n.get(this.#r[e])===e}*entries(){for(let e of this.#m())this.#e[e]!==void 0&&this.#r[e]!==void 0&&!this.#t(this.#e[e])&&(yield[this.#r[e],this.#e[e]])}*rentries(){for(let e of this.#E())this.#e[e]!==void 0&&this.#r[e]!==void 0&&!this.#t(this.#e[e])&&(yield[this.#r[e],this.#e[e]])}*keys(){for(let e of this.#m()){let r=this.#r[e];r!==void 0&&!this.#t(this.#e[e])&&(yield r)}}*rkeys(){for(let e of this.#E()){let r=this.#r[e];r!==void 0&&!this.#t(this.#e[e])&&(yield r)}}*values(){for(let e of this.#m())this.#e[e]!==void 0&&!this.#t(this.#e[e])&&(yield this.#e[e])}*rvalues(){for(let e of this.#E())this.#e[e]!==void 0&&!this.#t(this.#e[e])&&(yield this.#e[e])}[Symbol.iterator](){return this.entries()}find(e,r={}){for(let i of this.#m()){let n=this.#e[i],o=this.#t(n)?n.__staleWhileFetching:n;if(o!==void 0&&e(o,this.#r[i],this))return this.get(this.#r[i],r)}}forEach(e,r=this){for(let i of this.#m()){let n=this.#e[i],o=this.#t(n)?n.__staleWhileFetching:n;o!==void 0&&e.call(r,o,this.#r[i],this)}}rforEach(e,r=this){for(let i of this.#E()){let n=this.#e[i],o=this.#t(n)?n.__staleWhileFetching:n;o!==void 0&&e.call(r,o,this.#r[i],this)}}purgeStale(){let e=!1;for(let r of this.#E({allowStale:!0}))this.#d(r)&&(this.delete(this.#r[r]),e=!0);return e}dump(){let e=[];for(let r of this.#m({allowStale:!0})){let i=this.#r[r],n=this.#e[r],o=this.#t(n)?n.__staleWhileFetching:n;if(o===void 0||i===void 0)continue;let s={value:o};if(this.#w&&this.#S){s.ttl=this.#w[r];let l=Si.now()-this.#S[r];s.start=Math.floor(Date.now()-l)}this.#v&&(s.size=this.#v[r]),e.unshift([i,s])}return e}load(e){this.clear();for(let[r,i]of e){if(i.start){let n=Date.now()-i.start;i.start=Si.now()-n}this.set(r,i.value,i)}}set(e,r,i={}){if(r===void 0)return this.delete(e),this;let{ttl:n=this.ttl,start:o,noDisposeOnSet:s=this.noDisposeOnSet,sizeCalculation:l=this.sizeCalculation,status:u}=i,{noUpdateTTL:c=this.noUpdateTTL}=i,h=this.#U(e,r,i.size||0,l);if(this.maxEntrySize&&h>this.maxEntrySize)return u&&(u.set="miss",u.maxEntrySizeExceeded=!0),this.delete(e),this;let d=this.#i===0?void 0:this.#n.get(e);if(d===void 0)d=this.#i===0?this.#s:this.#b.length!==0?this.#b.pop():this.#i===this.#a?this.#x(!1):this.#i,this.#r[d]=e,this.#e[d]=r,this.#n.set(e,d),this.#u[this.#s]=d,this.#h[d]=this.#s,this.#s=d,this.#i++,this.#P(d,h,u),u&&(u.set="add"),c=!1;else{this.#C(d);let g=this.#e[d];if(r!==g){if(this.#I&&this.#t(g)){g.__abortController.abort(new Error("replaced"));let{__staleWhileFetching:y}=g;y!==void 0&&!s&&(this.#_&&this.#p?.(y,e,"set"),this.#f&&this.#o?.push([y,e,"set"]))}else s||(this.#_&&this.#p?.(g,e,"set"),this.#f&&this.#o?.push([g,e,"set"]));if(this.#R(d),this.#P(d,h,u),this.#e[d]=r,u){u.set="replace";let y=g&&this.#t(g)?g.__staleWhileFetching:g;y!==void 0&&(u.oldValue=y)}}else u&&(u.set="update")}if(n!==0&&!this.#w&&this.#k(),this.#w&&(c||this.#L(d,n,o),u&&this.#A(u,d)),!s&&this.#f&&this.#o){let g=this.#o,y;for(;y=g?.shift();)this.#g?.(...y)}return this}pop(){try{for(;this.#i;){let e=this.#e[this.#l];if(this.#x(!0),this.#t(e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(this.#f&&this.#o){let e=this.#o,r;for(;r=e?.shift();)this.#g?.(...r)}}}#x(e){let r=this.#l,i=this.#r[r],n=this.#e[r];return this.#I&&this.#t(n)?n.__abortController.abort(new Error("evicted")):(this.#_||this.#f)&&(this.#_&&this.#p?.(n,i,"evict"),this.#f&&this.#o?.push([n,i,"evict"])),this.#R(r),e&&(this.#r[r]=void 0,this.#e[r]=void 0,this.#b.push(r)),this.#i===1?(this.#l=this.#s=0,this.#b.length=0):this.#l=this.#u[r],this.#n.delete(i),this.#i--,r}has(e,r={}){let{updateAgeOnHas:i=this.updateAgeOnHas,status:n}=r,o=this.#n.get(e);if(o!==void 0){let s=this.#e[o];if(this.#t(s)&&s.__staleWhileFetching===void 0)return!1;if(this.#d(o))n&&(n.has="stale",this.#A(n,o));else return i&&this.#T(o),n&&(n.has="hit",this.#A(n,o)),!0}else n&&(n.has="miss");return!1}peek(e,r={}){let{allowStale:i=this.allowStale}=r,n=this.#n.get(e);if(n!==void 0&&(i||!this.#d(n))){let o=this.#e[n];return this.#t(o)?o.__staleWhileFetching:o}}#O(e,r,i,n){let o=r===void 0?void 0:this.#e[r];if(this.#t(o))return o;let s=new Tn,{signal:l}=i;l?.addEventListener("abort",()=>s.abort(l.reason),{signal:s.signal});let u={signal:s.signal,options:i,context:n},c=(S,A=!1)=>{let{aborted:I}=s.signal,P=i.ignoreFetchAbort&&S!==void 0;if(i.status&&(I&&!A?(i.status.fetchAborted=!0,i.status.fetchError=s.signal.reason,P&&(i.status.fetchAbortIgnored=!0)):i.status.fetchResolved=!0),I&&!P&&!A)return d(s.signal.reason);let R=y;return this.#e[r]===y&&(S===void 0?R.__staleWhileFetching?this.#e[r]=R.__staleWhileFetching:this.delete(e):(i.status&&(i.status.fetchUpdated=!0),this.set(e,S,u.options))),S},h=S=>(i.status&&(i.status.fetchRejected=!0,i.status.fetchError=S),d(S)),d=S=>{let{aborted:A}=s.signal,I=A&&i.allowStaleOnFetchAbort,P=I||i.allowStaleOnFetchRejection,R=P||i.noDeleteOnFetchRejection,M=y;if(this.#e[r]===y&&(!R||M.__staleWhileFetching===void 0?this.delete(e):I||(this.#e[r]=M.__staleWhileFetching)),P)return i.status&&M.__staleWhileFetching!==void 0&&(i.status.returnedStale=!0),M.__staleWhileFetching;if(M.__returned===M)throw S},g=(S,A)=>{let I=this.#B?.(e,o,u);I&&I instanceof Promise&&I.then(P=>S(P===void 0?void 0:P),A),s.signal.addEventListener("abort",()=>{(!i.ignoreFetchAbort||i.allowStaleOnFetchAbort)&&(S(void 0),i.allowStaleOnFetchAbort&&(S=P=>c(P,!0)))})};i.status&&(i.status.fetchDispatched=!0);let y=new Promise(g).then(c,h),w=Object.assign(y,{__abortController:s,__staleWhileFetching:o,__returned:void 0});return r===void 0?(this.set(e,w,{...u.options,status:void 0}),r=this.#n.get(e)):this.#e[r]=w,w}#t(e){if(!this.#I)return!1;let r=e;return!!r&&r instanceof Promise&&r.hasOwnProperty("__staleWhileFetching")&&r.__abortController instanceof Tn}async fetch(e,r={}){let{allowStale:i=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:o=this.noDeleteOnStaleGet,ttl:s=this.ttl,noDisposeOnSet:l=this.noDisposeOnSet,size:u=0,sizeCalculation:c=this.sizeCalculation,noUpdateTTL:h=this.noUpdateTTL,noDeleteOnFetchRejection:d=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:g=this.allowStaleOnFetchRejection,ignoreFetchAbort:y=this.ignoreFetchAbort,allowStaleOnFetchAbort:w=this.allowStaleOnFetchAbort,context:S,forceRefresh:A=!1,status:I,signal:P}=r;if(!this.#I)return I&&(I.fetch="get"),this.get(e,{allowStale:i,updateAgeOnGet:n,noDeleteOnStaleGet:o,status:I});let R={allowStale:i,updateAgeOnGet:n,noDeleteOnStaleGet:o,ttl:s,noDisposeOnSet:l,size:u,sizeCalculation:c,noUpdateTTL:h,noDeleteOnFetchRejection:d,allowStaleOnFetchRejection:g,allowStaleOnFetchAbort:w,ignoreFetchAbort:y,status:I,signal:P},M=this.#n.get(e);if(M===void 0){I&&(I.fetch="miss");let N=this.#O(e,M,R,S);return N.__returned=N}else{let N=this.#e[M];if(this.#t(N)){let ve=i&&N.__staleWhileFetching!==void 0;return I&&(I.fetch="inflight",ve&&(I.returnedStale=!0)),ve?N.__staleWhileFetching:N.__returned=N}let V=this.#d(M);if(!A&&!V)return I&&(I.fetch="hit"),this.#C(M),n&&this.#T(M),I&&this.#A(I,M),N;let Q=this.#O(e,M,R,S),Y=Q.__staleWhileFetching!==void 0&&i;return I&&(I.fetch=V?"stale":"refresh",Y&&V&&(I.returnedStale=!0)),Y?Q.__staleWhileFetching:Q.__returned=Q}}get(e,r={}){let{allowStale:i=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:o=this.noDeleteOnStaleGet,status:s}=r,l=this.#n.get(e);if(l!==void 0){let u=this.#e[l],c=this.#t(u);return s&&this.#A(s,l),this.#d(l)?(s&&(s.get="stale"),c?(s&&i&&u.__staleWhileFetching!==void 0&&(s.returnedStale=!0),i?u.__staleWhileFetching:void 0):(o||this.delete(e),s&&i&&(s.returnedStale=!0),i?u:void 0)):(s&&(s.get="hit"),c?u.__staleWhileFetching:(this.#C(l),n&&this.#T(l),u))}else s&&(s.get="miss")}#N(e,r){this.#h[r]=e,this.#u[e]=r}#C(e){e!==this.#s&&(e===this.#l?this.#l=this.#u[e]:this.#N(this.#h[e],this.#u[e]),this.#N(this.#s,e),this.#s=e)}delete(e){let r=!1;if(this.#i!==0){let i=this.#n.get(e);if(i!==void 0)if(r=!0,this.#i===1)this.clear();else{this.#R(i);let n=this.#e[i];this.#t(n)?n.__abortController.abort(new Error("deleted")):(this.#_||this.#f)&&(this.#_&&this.#p?.(n,e,"delete"),this.#f&&this.#o?.push([n,e,"delete"])),this.#n.delete(e),this.#r[i]=void 0,this.#e[i]=void 0,i===this.#s?this.#s=this.#h[i]:i===this.#l?this.#l=this.#u[i]:(this.#u[this.#h[i]]=this.#u[i],this.#h[this.#u[i]]=this.#h[i]),this.#i--,this.#b.push(i)}}if(this.#f&&this.#o?.length){let i=this.#o,n;for(;n=i?.shift();)this.#g?.(...n)}return r}clear(){for(let e of this.#E({allowStale:!0})){let r=this.#e[e];if(this.#t(r))r.__abortController.abort(new Error("deleted"));else{let i=this.#r[e];this.#_&&this.#p?.(r,i,"delete"),this.#f&&this.#o?.push([r,i,"delete"])}}if(this.#n.clear(),this.#e.fill(void 0),this.#r.fill(void 0),this.#w&&this.#S&&(this.#w.fill(0),this.#S.fill(0)),this.#v&&this.#v.fill(0),this.#l=0,this.#s=0,this.#b.length=0,this.#y=0,this.#i=0,this.#f&&this.#o){let e=this.#o,r;for(;r=e?.shift();)this.#g?.(...r)}}};Rn.LRUCache=Jo});var lt=L(Wt=>{"use strict";_();E();m();Object.defineProperty(Wt,"t",{value:!0});Wt.ContainerIterator=Wt.Container=Wt.Base=void 0;var Xo=class{constructor(e=0){this.iteratorType=e}equals(e){return this.o===e.o}};Wt.ContainerIterator=Xo;var Cn=class{constructor(){this.i=0}get length(){return this.i}size(){return this.i}empty(){return this.i===0}};Wt.Base=Cn;var Zo=class extends Cn{};Wt.Container=Zo});var Kd=L(Bn=>{"use strict";_();E();m();Object.defineProperty(Bn,"t",{value:!0});Bn.default=void 0;var Zm=lt(),el=class extends Zm.Base{constructor(e=[]){super(),this.S=[];let r=this;e.forEach(function(i){r.push(i)})}clear(){this.i=0,this.S=[]}push(e){return this.S.push(e),this.i+=1,this.i}pop(){if(this.i!==0)return this.i-=1,this.S.pop()}top(){return this.S[this.i-1]}},e1=el;Bn.default=e1});var Qd=L(Pn=>{"use strict";_();E();m();Object.defineProperty(Pn,"t",{value:!0});Pn.default=void 0;var t1=lt(),tl=class extends t1.Base{constructor(e=[]){super(),this.j=0,this.q=[];let r=this;e.forEach(function(i){r.push(i)})}clear(){this.q=[],this.i=this.j=0}push(e){let r=this.q.length;if(this.j/r>.5&&this.j+this.i>=r&&r>4096){let i=this.i;for(let n=0;n<i;++n)this.q[n]=this.q[this.j+n];this.j=0,this.q[this.i]=e}else this.q[this.j+this.i]=e;return++this.i}pop(){if(this.i===0)return;let e=this.q[this.j++];return this.i-=1,e}front(){if(this.i!==0)return this.q[this.j]}},r1=tl;Pn.default=r1});var Gd=L(xn=>{"use strict";_();E();m();Object.defineProperty(xn,"t",{value:!0});xn.default=void 0;var i1=lt(),rl=class extends i1.Base{constructor(e=[],r=function(n,o){return n>o?-1:n<o?1:0},i=!0){if(super(),this.v=r,Array.isArray(e))this.C=i?[...e]:e;else{this.C=[];let o=this;e.forEach(function(s){o.C.push(s)})}this.i=this.C.length;let n=this.i>>1;for(let o=this.i-1>>1;o>=0;--o)this.k(o,n)}m(e){let r=this.C[e];for(;e>0;){let i=e-1>>1,n=this.C[i];if(this.v(n,r)<=0)break;this.C[e]=n,e=i}this.C[e]=r}k(e,r){let i=this.C[e];for(;e<r;){let n=e<<1|1,o=n+1,s=this.C[n];if(o<this.i&&this.v(s,this.C[o])>0&&(n=o,s=this.C[o]),this.v(s,i)>=0)break;this.C[e]=s,e=n}this.C[e]=i}clear(){this.i=0,this.C.length=0}push(e){this.C.push(e),this.m(this.i),this.i+=1}pop(){if(this.i===0)return;let e=this.C[0],r=this.C.pop();return this.i-=1,this.i&&(this.C[0]=r,this.k(0,this.i>>1)),e}top(){return this.C[0]}find(e){return this.C.indexOf(e)>=0}remove(e){let r=this.C.indexOf(e);return r<0?!1:(r===0?this.pop():r===this.i-1?(this.C.pop(),this.i-=1):(this.C.splice(r,1,this.C.pop()),this.i-=1,this.m(r),this.k(r,this.i>>1)),!0)}updateItem(e){let r=this.C.indexOf(e);return r<0?!1:(this.m(r),this.k(r,this.i>>1),!0)}toArray(){return[...this.C]}},n1=rl;xn.default=n1});var kn=L(On=>{"use strict";_();E();m();Object.defineProperty(On,"t",{value:!0});On.default=void 0;var s1=lt(),il=class extends s1.Container{},o1=il;On.default=o1});var at=L(nl=>{"use strict";_();E();m();Object.defineProperty(nl,"t",{value:!0});nl.throwIteratorAccessError=l1;function l1(){throw new RangeError("Iterator access denied!")}});var ol=L(Un=>{"use strict";_();E();m();Object.defineProperty(Un,"t",{value:!0});Un.RandomIterator=void 0;var a1=lt(),Ln=at(),sl=class extends a1.ContainerIterator{constructor(e,r){super(r),this.o=e,this.iteratorType===0?(this.pre=function(){return this.o===0&&(0,Ln.throwIteratorAccessError)(),this.o-=1,this},this.next=function(){return this.o===this.container.size()&&(0,Ln.throwIteratorAccessError)(),this.o+=1,this}):(this.pre=function(){return this.o===this.container.size()-1&&(0,Ln.throwIteratorAccessError)(),this.o+=1,this},this.next=function(){return this.o===-1&&(0,Ln.throwIteratorAccessError)(),this.o-=1,this})}get pointer(){return this.container.getElementByPos(this.o)}set pointer(e){this.container.setElementByPos(this.o,e)}};Un.RandomIterator=sl});var Yd=L(Mn=>{"use strict";_();E();m();Object.defineProperty(Mn,"t",{value:!0});Mn.default=void 0;var u1=c1(kn()),f1=ol();function c1(t){return t&&t.t?t:{default:t}}var gr=class t extends f1.RandomIterator{constructor(e,r,i){super(e,i),this.container=r}copy(){return new t(this.o,this.container,this.iteratorType)}},ll=class extends u1.default{constructor(e=[],r=!0){if(super(),Array.isArray(e))this.J=r?[...e]:e,this.i=e.length;else{this.J=[];let i=this;e.forEach(function(n){i.pushBack(n)})}}clear(){this.i=0,this.J.length=0}begin(){return new gr(0,this)}end(){return new gr(this.i,this)}rBegin(){return new gr(this.i-1,this,1)}rEnd(){return new gr(-1,this,1)}front(){return this.J[0]}back(){return this.J[this.i-1]}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;return this.J[e]}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;return this.J.splice(e,1),this.i-=1,this.i}eraseElementByValue(e){let r=0;for(let i=0;i<this.i;++i)this.J[i]!==e&&(this.J[r++]=this.J[i]);return this.i=this.J.length=r,this.i}eraseElementByIterator(e){let r=e.o;return e=e.next(),this.eraseElementByPos(r),e}pushBack(e){return this.J.push(e),this.i+=1,this.i}popBack(){if(this.i!==0)return this.i-=1,this.J.pop()}setElementByPos(e,r){if(e<0||e>this.i-1)throw new RangeError;this.J[e]=r}insert(e,r,i=1){if(e<0||e>this.i)throw new RangeError;return this.J.splice(e,0,...new Array(i).fill(r)),this.i+=i,this.i}find(e){for(let r=0;r<this.i;++r)if(this.J[r]===e)return new gr(r,this);return this.end()}reverse(){this.J.reverse()}unique(){let e=1;for(let r=1;r<this.i;++r)this.J[r]!==this.J[r-1]&&(this.J[e++]=this.J[r]);return this.i=this.J.length=e,this.i}sort(e){this.J.sort(e)}forEach(e){for(let r=0;r<this.i;++r)e(this.J[r],r,this)}[Symbol.iterator](){return function*(){yield*this.J}.bind(this)()}},h1=ll;Mn.default=h1});var Jd=L(Nn=>{"use strict";_();E();m();Object.defineProperty(Nn,"t",{value:!0});Nn.default=void 0;var d1=g1(kn()),p1=lt(),yr=at();function g1(t){return t&&t.t?t:{default:t}}var br=class t extends p1.ContainerIterator{constructor(e,r,i,n){super(n),this.o=e,this.h=r,this.container=i,this.iteratorType===0?(this.pre=function(){return this.o.L===this.h&&(0,yr.throwIteratorAccessError)(),this.o=this.o.L,this},this.next=function(){return this.o===this.h&&(0,yr.throwIteratorAccessError)(),this.o=this.o.B,this}):(this.pre=function(){return this.o.B===this.h&&(0,yr.throwIteratorAccessError)(),this.o=this.o.B,this},this.next=function(){return this.o===this.h&&(0,yr.throwIteratorAccessError)(),this.o=this.o.L,this})}get pointer(){return this.o===this.h&&(0,yr.throwIteratorAccessError)(),this.o.l}set pointer(e){this.o===this.h&&(0,yr.throwIteratorAccessError)(),this.o.l=e}copy(){return new t(this.o,this.h,this.container,this.iteratorType)}},al=class extends d1.default{constructor(e=[]){super(),this.h={},this.p=this._=this.h.L=this.h.B=this.h;let r=this;e.forEach(function(i){r.pushBack(i)})}V(e){let{L:r,B:i}=e;r.B=i,i.L=r,e===this.p&&(this.p=i),e===this._&&(this._=r),this.i-=1}G(e,r){let i=r.B,n={l:e,L:r,B:i};r.B=n,i.L=n,r===this.h&&(this.p=n),i===this.h&&(this._=n),this.i+=1}clear(){this.i=0,this.p=this._=this.h.L=this.h.B=this.h}begin(){return new br(this.p,this.h,this)}end(){return new br(this.h,this.h,this)}rBegin(){return new br(this._,this.h,this,1)}rEnd(){return new br(this.h,this.h,this,1)}front(){return this.p.l}back(){return this._.l}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;return r.l}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;return this.V(r),this.i}eraseElementByValue(e){let r=this.p;for(;r!==this.h;)r.l===e&&this.V(r),r=r.B;return this.i}eraseElementByIterator(e){let r=e.o;return r===this.h&&(0,yr.throwIteratorAccessError)(),e=e.next(),this.V(r),e}pushBack(e){return this.G(e,this._),this.i}popBack(){if(this.i===0)return;let e=this._.l;return this.V(this._),e}pushFront(e){return this.G(e,this.h),this.i}popFront(){if(this.i===0)return;let e=this.p.l;return this.V(this.p),e}setElementByPos(e,r){if(e<0||e>this.i-1)throw new RangeError;let i=this.p;for(;e--;)i=i.B;i.l=r}insert(e,r,i=1){if(e<0||e>this.i)throw new RangeError;if(i<=0)return this.i;if(e===0)for(;i--;)this.pushFront(r);else if(e===this.i)for(;i--;)this.pushBack(r);else{let n=this.p;for(let s=1;s<e;++s)n=n.B;let o=n.B;for(this.i+=i;i--;)n.B={l:r,L:n},n.B.L=n,n=n.B;n.B=o,o.L=n}return this.i}find(e){let r=this.p;for(;r!==this.h;){if(r.l===e)return new br(r,this.h,this);r=r.B}return this.end()}reverse(){if(this.i<=1)return;let e=this.p,r=this._,i=0;for(;i<<1<this.i;){let n=e.l;e.l=r.l,r.l=n,e=e.B,r=r.L,i+=1}}unique(){if(this.i<=1)return this.i;let e=this.p;for(;e!==this.h;){let r=e;for(;r.B!==this.h&&r.l===r.B.l;)r=r.B,this.i-=1;e.B=r.B,e.B.L=e,e=e.B}return this.i}sort(e){if(this.i<=1)return;let r=[];this.forEach(function(n){r.push(n)}),r.sort(e);let i=this.p;r.forEach(function(n){i.l=n,i=i.B})}merge(e){let r=this;if(this.i===0)e.forEach(function(i){r.pushBack(i)});else{let i=this.p;e.forEach(function(n){for(;i!==r.h&&i.l<=n;)i=i.B;r.G(n,i.L)})}return this.i}forEach(e){let r=this.p,i=0;for(;r!==this.h;)e(r.l,i++,this),r=r.B}[Symbol.iterator](){return function*(){if(this.i===0)return;let e=this.p;for(;e!==this.h;)yield e.l,e=e.B}.bind(this)()}},y1=al;Nn.default=y1});var Xd=L(qn=>{"use strict";_();E();m();Object.defineProperty(qn,"t",{value:!0});qn.default=void 0;var b1=_1(kn()),w1=ol();function _1(t){return t&&t.t?t:{default:t}}var wr=class t extends w1.RandomIterator{constructor(e,r,i){super(e,i),this.container=r}copy(){return new t(this.o,this.container,this.iteratorType)}},ul=class extends b1.default{constructor(e=[],r=4096){super(),this.j=0,this.D=0,this.R=0,this.N=0,this.P=0,this.A=[];let i=(()=>{if(typeof e.length=="number")return e.length;if(typeof e.size=="number")return e.size;if(typeof e.size=="function")return e.size();throw new TypeError("Cannot get the length or size of the container")})();this.F=r,this.P=Math.max(Math.ceil(i/this.F),1);for(let s=0;s<this.P;++s)this.A.push(new Array(this.F));let n=Math.ceil(i/this.F);this.j=this.R=(this.P>>1)-(n>>1),this.D=this.N=this.F-i%this.F>>1;let o=this;e.forEach(function(s){o.pushBack(s)})}T(){let e=[],r=Math.max(this.P>>1,1);for(let i=0;i<r;++i)e[i]=new Array(this.F);for(let i=this.j;i<this.P;++i)e[e.length]=this.A[i];for(let i=0;i<this.R;++i)e[e.length]=this.A[i];e[e.length]=[...this.A[this.R]],this.j=r,this.R=e.length-1;for(let i=0;i<r;++i)e[e.length]=new Array(this.F);this.A=e,this.P=e.length}O(e){let r=this.D+e+1,i=r%this.F,n=i-1,o=this.j+(r-i)/this.F;return i===0&&(o-=1),o%=this.P,n<0&&(n+=this.F),{curNodeBucketIndex:o,curNodePointerIndex:n}}clear(){this.A=[new Array(this.F)],this.P=1,this.j=this.R=this.i=0,this.D=this.N=this.F>>1}begin(){return new wr(0,this)}end(){return new wr(this.i,this)}rBegin(){return new wr(this.i-1,this,1)}rEnd(){return new wr(-1,this,1)}front(){if(this.i!==0)return this.A[this.j][this.D]}back(){if(this.i!==0)return this.A[this.R][this.N]}pushBack(e){return this.i&&(this.N<this.F-1?this.N+=1:this.R<this.P-1?(this.R+=1,this.N=0):(this.R=0,this.N=0),this.R===this.j&&this.N===this.D&&this.T()),this.i+=1,this.A[this.R][this.N]=e,this.i}popBack(){if(this.i===0)return;let e=this.A[this.R][this.N];return this.i!==1&&(this.N>0?this.N-=1:this.R>0?(this.R-=1,this.N=this.F-1):(this.R=this.P-1,this.N=this.F-1)),this.i-=1,e}pushFront(e){return this.i&&(this.D>0?this.D-=1:this.j>0?(this.j-=1,this.D=this.F-1):(this.j=this.P-1,this.D=this.F-1),this.j===this.R&&this.D===this.N&&this.T()),this.i+=1,this.A[this.j][this.D]=e,this.i}popFront(){if(this.i===0)return;let e=this.A[this.j][this.D];return this.i!==1&&(this.D<this.F-1?this.D+=1:this.j<this.P-1?(this.j+=1,this.D=0):(this.j=0,this.D=0)),this.i-=1,e}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let{curNodeBucketIndex:r,curNodePointerIndex:i}=this.O(e);return this.A[r][i]}setElementByPos(e,r){if(e<0||e>this.i-1)throw new RangeError;let{curNodeBucketIndex:i,curNodePointerIndex:n}=this.O(e);this.A[i][n]=r}insert(e,r,i=1){if(e<0||e>this.i)throw new RangeError;if(e===0)for(;i--;)this.pushFront(r);else if(e===this.i)for(;i--;)this.pushBack(r);else{let n=[];for(let o=e;o<this.i;++o)n.push(this.getElementByPos(o));this.cut(e-1);for(let o=0;o<i;++o)this.pushBack(r);for(let o=0;o<n.length;++o)this.pushBack(n[o])}return this.i}cut(e){if(e<0)return this.clear(),0;let{curNodeBucketIndex:r,curNodePointerIndex:i}=this.O(e);return this.R=r,this.N=i,this.i=e+1,this.i}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;if(e===0)this.popFront();else if(e===this.i-1)this.popBack();else{let r=[];for(let n=e+1;n<this.i;++n)r.push(this.getElementByPos(n));this.cut(e),this.popBack();let i=this;r.forEach(function(n){i.pushBack(n)})}return this.i}eraseElementByValue(e){if(this.i===0)return 0;let r=[];for(let n=0;n<this.i;++n){let o=this.getElementByPos(n);o!==e&&r.push(o)}let i=r.length;for(let n=0;n<i;++n)this.setElementByPos(n,r[n]);return this.cut(i-1)}eraseElementByIterator(e){let r=e.o;return this.eraseElementByPos(r),e=e.next(),e}find(e){for(let r=0;r<this.i;++r)if(this.getElementByPos(r)===e)return new wr(r,this);return this.end()}reverse(){let e=0,r=this.i-1;for(;e<r;){let i=this.getElementByPos(e);this.setElementByPos(e,this.getElementByPos(r)),this.setElementByPos(r,i),e+=1,r-=1}}unique(){if(this.i<=1)return this.i;let e=1,r=this.getElementByPos(0);for(let i=1;i<this.i;++i){let n=this.getElementByPos(i);n!==r&&(r=n,this.setElementByPos(e++,n))}for(;this.i>e;)this.popBack();return this.i}sort(e){let r=[];for(let i=0;i<this.i;++i)r.push(this.getElementByPos(i));r.sort(e);for(let i=0;i<this.i;++i)this.setElementByPos(i,r[i])}shrinkToFit(){if(this.i===0)return;let e=[];this.forEach(function(r){e.push(r)}),this.P=Math.max(Math.ceil(this.i/this.F),1),this.i=this.j=this.R=this.D=this.N=0,this.A=[];for(let r=0;r<this.P;++r)this.A.push(new Array(this.F));for(let r=0;r<e.length;++r)this.pushBack(e[r])}forEach(e){for(let r=0;r<this.i;++r)e(this.getElementByPos(r),r,this)}[Symbol.iterator](){return function*(){for(let e=0;e<this.i;++e)yield this.getElementByPos(e)}.bind(this)()}},m1=ul;qn.default=m1});var Zd=L(Gr=>{"use strict";_();E();m();Object.defineProperty(Gr,"t",{value:!0});Gr.TreeNodeEnableIndex=Gr.TreeNode=void 0;var Dn=class{constructor(e,r){this.ee=1,this.u=void 0,this.l=void 0,this.U=void 0,this.W=void 0,this.tt=void 0,this.u=e,this.l=r}L(){let e=this;if(e.ee===1&&e.tt.tt===e)e=e.W;else if(e.U)for(e=e.U;e.W;)e=e.W;else{let r=e.tt;for(;r.U===e;)e=r,r=e.tt;e=r}return e}B(){let e=this;if(e.W){for(e=e.W;e.U;)e=e.U;return e}else{let r=e.tt;for(;r.W===e;)e=r,r=e.tt;return e.W!==r?r:e}}te(){let e=this.tt,r=this.W,i=r.U;return e.tt===this?e.tt=r:e.U===this?e.U=r:e.W=r,r.tt=e,r.U=this,this.tt=r,this.W=i,i&&(i.tt=this),r}se(){let e=this.tt,r=this.U,i=r.W;return e.tt===this?e.tt=r:e.U===this?e.U=r:e.W=r,r.tt=e,r.W=this,this.tt=r,this.U=i,i&&(i.tt=this),r}};Gr.TreeNode=Dn;var fl=class extends Dn{constructor(){super(...arguments),this.rt=1}te(){let e=super.te();return this.ie(),e.ie(),e}se(){let e=super.se();return this.ie(),e.ie(),e}ie(){this.rt=1,this.U&&(this.rt+=this.U.rt),this.W&&(this.rt+=this.W.rt)}};Gr.TreeNodeEnableIndex=fl});var hl=L(jn=>{"use strict";_();E();m();Object.defineProperty(jn,"t",{value:!0});jn.default=void 0;var ep=Zd(),E1=lt(),tp=at(),cl=class extends E1.Container{constructor(e=function(i,n){return i<n?-1:i>n?1:0},r=!1){super(),this.Y=void 0,this.v=e,r?(this.re=ep.TreeNodeEnableIndex,this.M=function(i,n,o){let s=this.ne(i,n,o);if(s){let l=s.tt;for(;l!==this.h;)l.rt+=1,l=l.tt;let u=this.he(s);if(u){let{parentNode:c,grandParent:h,curNode:d}=u;c.ie(),h.ie(),d.ie()}}return this.i},this.V=function(i){let n=this.fe(i);for(;n!==this.h;)n.rt-=1,n=n.tt}):(this.re=ep.TreeNode,this.M=function(i,n,o){let s=this.ne(i,n,o);return s&&this.he(s),this.i},this.V=this.fe),this.h=new this.re}X(e,r){let i=this.h;for(;e;){let n=this.v(e.u,r);if(n<0)e=e.W;else if(n>0)i=e,e=e.U;else return e}return i}Z(e,r){let i=this.h;for(;e;)this.v(e.u,r)<=0?e=e.W:(i=e,e=e.U);return i}$(e,r){let i=this.h;for(;e;){let n=this.v(e.u,r);if(n<0)i=e,e=e.W;else if(n>0)e=e.U;else return e}return i}rr(e,r){let i=this.h;for(;e;)this.v(e.u,r)<0?(i=e,e=e.W):e=e.U;return i}ue(e){for(;;){let r=e.tt;if(r===this.h)return;if(e.ee===1){e.ee=0;return}if(e===r.U){let i=r.W;if(i.ee===1)i.ee=0,r.ee=1,r===this.Y?this.Y=r.te():r.te();else if(i.W&&i.W.ee===1){i.ee=r.ee,r.ee=0,i.W.ee=0,r===this.Y?this.Y=r.te():r.te();return}else i.U&&i.U.ee===1?(i.ee=1,i.U.ee=0,i.se()):(i.ee=1,e=r)}else{let i=r.U;if(i.ee===1)i.ee=0,r.ee=1,r===this.Y?this.Y=r.se():r.se();else if(i.U&&i.U.ee===1){i.ee=r.ee,r.ee=0,i.U.ee=0,r===this.Y?this.Y=r.se():r.se();return}else i.W&&i.W.ee===1?(i.ee=1,i.W.ee=0,i.te()):(i.ee=1,e=r)}}}fe(e){if(this.i===1)return this.clear(),this.h;let r=e;for(;r.U||r.W;){if(r.W)for(r=r.W;r.U;)r=r.U;else r=r.U;[e.u,r.u]=[r.u,e.u],[e.l,r.l]=[r.l,e.l],e=r}this.h.U===r?this.h.U=r.tt:this.h.W===r&&(this.h.W=r.tt),this.ue(r);let i=r.tt;return r===i.U?i.U=void 0:i.W=void 0,this.i-=1,this.Y.ee=0,i}oe(e,r){return e===void 0?!1:this.oe(e.U,r)||r(e)?!0:this.oe(e.W,r)}he(e){for(;;){let r=e.tt;if(r.ee===0)return;let i=r.tt;if(r===i.U){let n=i.W;if(n&&n.ee===1){if(n.ee=r.ee=0,i===this.Y)return;i.ee=1,e=i;continue}else if(e===r.W){if(e.ee=0,e.U&&(e.U.tt=r),e.W&&(e.W.tt=i),r.W=e.U,i.U=e.W,e.U=r,e.W=i,i===this.Y)this.Y=e,this.h.tt=e;else{let o=i.tt;o.U===i?o.U=e:o.W=e}return e.tt=i.tt,r.tt=e,i.tt=e,i.ee=1,{parentNode:r,grandParent:i,curNode:e}}else r.ee=0,i===this.Y?this.Y=i.se():i.se(),i.ee=1}else{let n=i.U;if(n&&n.ee===1){if(n.ee=r.ee=0,i===this.Y)return;i.ee=1,e=i;continue}else if(e===r.U){if(e.ee=0,e.U&&(e.U.tt=i),e.W&&(e.W.tt=r),i.W=e.U,r.U=e.W,e.U=i,e.W=r,i===this.Y)this.Y=e,this.h.tt=e;else{let o=i.tt;o.U===i?o.U=e:o.W=e}return e.tt=i.tt,r.tt=e,i.tt=e,i.ee=1,{parentNode:r,grandParent:i,curNode:e}}else r.ee=0,i===this.Y?this.Y=i.te():i.te(),i.ee=1}return}}ne(e,r,i){if(this.Y===void 0){this.i+=1,this.Y=new this.re(e,r),this.Y.ee=0,this.Y.tt=this.h,this.h.tt=this.Y,this.h.U=this.Y,this.h.W=this.Y;return}let n,o=this.h.U,s=this.v(o.u,e);if(s===0){o.l=r;return}else if(s>0)o.U=new this.re(e,r),o.U.tt=o,n=o.U,this.h.U=n;else{let l=this.h.W,u=this.v(l.u,e);if(u===0){l.l=r;return}else if(u<0)l.W=new this.re(e,r),l.W.tt=l,n=l.W,this.h.W=n;else{if(i!==void 0){let c=i.o;if(c!==this.h){let h=this.v(c.u,e);if(h===0){c.l=r;return}else if(h>0){let d=c.L(),g=this.v(d.u,e);if(g===0){d.l=r;return}else g<0&&(n=new this.re(e,r),d.W===void 0?(d.W=n,n.tt=d):(c.U=n,n.tt=c))}}}if(n===void 0)for(n=this.Y;;){let c=this.v(n.u,e);if(c>0){if(n.U===void 0){n.U=new this.re(e,r),n.U.tt=n,n=n.U;break}n=n.U}else if(c<0){if(n.W===void 0){n.W=new this.re(e,r),n.W.tt=n,n=n.W;break}n=n.W}else{n.l=r;return}}}}return this.i+=1,n}I(e,r){for(;e;){let i=this.v(e.u,r);if(i<0)e=e.W;else if(i>0)e=e.U;else return e}return e||this.h}clear(){this.i=0,this.Y=void 0,this.h.tt=void 0,this.h.U=this.h.W=void 0}updateKeyByIterator(e,r){let i=e.o;if(i===this.h&&(0,tp.throwIteratorAccessError)(),this.i===1)return i.u=r,!0;if(i===this.h.U)return this.v(i.B().u,r)>0?(i.u=r,!0):!1;if(i===this.h.W)return this.v(i.L().u,r)<0?(i.u=r,!0):!1;let n=i.L().u;if(this.v(n,r)>=0)return!1;let o=i.B().u;return this.v(o,r)<=0?!1:(i.u=r,!0)}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=0,i=this;return this.oe(this.Y,function(n){return e===r?(i.V(n),!0):(r+=1,!1)}),this.i}eraseElementByKey(e){if(this.i===0)return!1;let r=this.I(this.Y,e);return r===this.h?!1:(this.V(r),!0)}eraseElementByIterator(e){let r=e.o;r===this.h&&(0,tp.throwIteratorAccessError)();let i=r.W===void 0;return e.iteratorType===0?i&&e.next():(!i||r.U===void 0)&&e.next(),this.V(r),e}forEach(e){let r=0;for(let i of this)e(i,r++,this)}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r,i=0;for(let n of this){if(i===e){r=n;break}i+=1}return r}getHeight(){if(this.i===0)return 0;let e=function(r){return r?Math.max(e(r.U),e(r.W))+1:0};return e(this.Y)}},v1=cl;jn.default=v1});var pl=L(Wn=>{"use strict";_();E();m();Object.defineProperty(Wn,"t",{value:!0});Wn.default=void 0;var S1=lt(),Fn=at(),dl=class extends S1.ContainerIterator{constructor(e,r,i){super(i),this.o=e,this.h=r,this.iteratorType===0?(this.pre=function(){return this.o===this.h.U&&(0,Fn.throwIteratorAccessError)(),this.o=this.o.L(),this},this.next=function(){return this.o===this.h&&(0,Fn.throwIteratorAccessError)(),this.o=this.o.B(),this}):(this.pre=function(){return this.o===this.h.W&&(0,Fn.throwIteratorAccessError)(),this.o=this.o.B(),this},this.next=function(){return this.o===this.h&&(0,Fn.throwIteratorAccessError)(),this.o=this.o.L(),this})}get index(){let e=this.o,r=this.h.tt;if(e===this.h)return r?r.rt-1:0;let i=0;for(e.U&&(i+=e.U.rt);e!==r;){let n=e.tt;e===n.W&&(i+=1,n.U&&(i+=n.U.rt)),e=n}return i}},A1=dl;Wn.default=A1});var ip=L($n=>{"use strict";_();E();m();Object.defineProperty($n,"t",{value:!0});$n.default=void 0;var I1=rp(hl()),T1=rp(pl()),R1=at();function rp(t){return t&&t.t?t:{default:t}}var Ke=class t extends T1.default{constructor(e,r,i,n){super(e,r,n),this.container=i}get pointer(){return this.o===this.h&&(0,R1.throwIteratorAccessError)(),this.o.u}copy(){return new t(this.o,this.h,this.container,this.iteratorType)}},gl=class extends I1.default{constructor(e=[],r,i){super(r,i);let n=this;e.forEach(function(o){n.insert(o)})}*K(e){e!==void 0&&(yield*this.K(e.U),yield e.u,yield*this.K(e.W))}begin(){return new Ke(this.h.U||this.h,this.h,this)}end(){return new Ke(this.h,this.h,this)}rBegin(){return new Ke(this.h.W||this.h,this.h,this,1)}rEnd(){return new Ke(this.h,this.h,this,1)}front(){return this.h.U?this.h.U.u:void 0}back(){return this.h.W?this.h.W.u:void 0}insert(e,r){return this.M(e,void 0,r)}find(e){let r=this.I(this.Y,e);return new Ke(r,this.h,this)}lowerBound(e){let r=this.X(this.Y,e);return new Ke(r,this.h,this)}upperBound(e){let r=this.Z(this.Y,e);return new Ke(r,this.h,this)}reverseLowerBound(e){let r=this.$(this.Y,e);return new Ke(r,this.h,this)}reverseUpperBound(e){let r=this.rr(this.Y,e);return new Ke(r,this.h,this)}union(e){let r=this;return e.forEach(function(i){r.insert(i)}),this.i}[Symbol.iterator](){return this.K(this.Y)}},C1=gl;$n.default=C1});var sp=L(Hn=>{"use strict";_();E();m();Object.defineProperty(Hn,"t",{value:!0});Hn.default=void 0;var B1=np(hl()),P1=np(pl()),x1=at();function np(t){return t&&t.t?t:{default:t}}var Qe=class t extends P1.default{constructor(e,r,i,n){super(e,r,n),this.container=i}get pointer(){this.o===this.h&&(0,x1.throwIteratorAccessError)();let e=this;return new Proxy([],{get(r,i){if(i==="0")return e.o.u;if(i==="1")return e.o.l},set(r,i,n){if(i!=="1")throw new TypeError("props must be 1");return e.o.l=n,!0}})}copy(){return new t(this.o,this.h,this.container,this.iteratorType)}},yl=class extends B1.default{constructor(e=[],r,i){super(r,i);let n=this;e.forEach(function(o){n.setElement(o[0],o[1])})}*K(e){e!==void 0&&(yield*this.K(e.U),yield[e.u,e.l],yield*this.K(e.W))}begin(){return new Qe(this.h.U||this.h,this.h,this)}end(){return new Qe(this.h,this.h,this)}rBegin(){return new Qe(this.h.W||this.h,this.h,this,1)}rEnd(){return new Qe(this.h,this.h,this,1)}front(){if(this.i===0)return;let e=this.h.U;return[e.u,e.l]}back(){if(this.i===0)return;let e=this.h.W;return[e.u,e.l]}lowerBound(e){let r=this.X(this.Y,e);return new Qe(r,this.h,this)}upperBound(e){let r=this.Z(this.Y,e);return new Qe(r,this.h,this)}reverseLowerBound(e){let r=this.$(this.Y,e);return new Qe(r,this.h,this)}reverseUpperBound(e){let r=this.rr(this.Y,e);return new Qe(r,this.h,this)}setElement(e,r,i){return this.M(e,r,i)}find(e){let r=this.I(this.Y,e);return new Qe(r,this.h,this)}getElementByKey(e){return this.I(this.Y,e).l}union(e){let r=this;return e.forEach(function(i){r.setElement(i[0],i[1])}),this.i}[Symbol.iterator](){return this.K(this.Y)}},O1=yl;Hn.default=O1});var wl=L(bl=>{"use strict";_();E();m();Object.defineProperty(bl,"t",{value:!0});bl.default=k1;function k1(t){let e=typeof t;return e==="object"&&t!==null||e==="function"}});var vl=L(Yr=>{"use strict";_();E();m();Object.defineProperty(Yr,"t",{value:!0});Yr.HashContainerIterator=Yr.HashContainer=void 0;var op=lt(),_l=L1(wl()),Ai=at();function L1(t){return t&&t.t?t:{default:t}}var ml=class extends op.ContainerIterator{constructor(e,r,i){super(i),this.o=e,this.h=r,this.iteratorType===0?(this.pre=function(){return this.o.L===this.h&&(0,Ai.throwIteratorAccessError)(),this.o=this.o.L,this},this.next=function(){return this.o===this.h&&(0,Ai.throwIteratorAccessError)(),this.o=this.o.B,this}):(this.pre=function(){return this.o.B===this.h&&(0,Ai.throwIteratorAccessError)(),this.o=this.o.B,this},this.next=function(){return this.o===this.h&&(0,Ai.throwIteratorAccessError)(),this.o=this.o.L,this})}};Yr.HashContainerIterator=ml;var El=class extends op.Container{constructor(){super(),this.H=[],this.g={},this.HASH_TAG=Symbol("@@HASH_TAG"),Object.setPrototypeOf(this.g,null),this.h={},this.h.L=this.h.B=this.p=this._=this.h}V(e){let{L:r,B:i}=e;r.B=i,i.L=r,e===this.p&&(this.p=i),e===this._&&(this._=r),this.i-=1}M(e,r,i){i===void 0&&(i=(0,_l.default)(e));let n;if(i){let o=e[this.HASH_TAG];if(o!==void 0)return this.H[o].l=r,this.i;Object.defineProperty(e,this.HASH_TAG,{value:this.H.length,configurable:!0}),n={u:e,l:r,L:this._,B:this.h},this.H.push(n)}else{let o=this.g[e];if(o)return o.l=r,this.i;n={u:e,l:r,L:this._,B:this.h},this.g[e]=n}return this.i===0?(this.p=n,this.h.B=n):this._.B=n,this._=n,this.h.L=n,++this.i}I(e,r){if(r===void 0&&(r=(0,_l.default)(e)),r){let i=e[this.HASH_TAG];return i===void 0?this.h:this.H[i]}else return this.g[e]||this.h}clear(){let e=this.HASH_TAG;this.H.forEach(function(r){delete r.u[e]}),this.H=[],this.g={},Object.setPrototypeOf(this.g,null),this.i=0,this.p=this._=this.h.L=this.h.B=this.h}eraseElementByKey(e,r){let i;if(r===void 0&&(r=(0,_l.default)(e)),r){let n=e[this.HASH_TAG];if(n===void 0)return!1;delete e[this.HASH_TAG],i=this.H[n],delete this.H[n]}else{if(i=this.g[e],i===void 0)return!1;delete this.g[e]}return this.V(i),!0}eraseElementByIterator(e){let r=e.o;return r===this.h&&(0,Ai.throwIteratorAccessError)(),this.V(r),e.next()}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;return this.V(r),this.i}};Yr.HashContainer=El});var ap=L(Vn=>{"use strict";_();E();m();Object.defineProperty(Vn,"t",{value:!0});Vn.default=void 0;var lp=vl(),U1=at(),_r=class t extends lp.HashContainerIterator{constructor(e,r,i,n){super(e,r,n),this.container=i}get pointer(){return this.o===this.h&&(0,U1.throwIteratorAccessError)(),this.o.u}copy(){return new t(this.o,this.h,this.container,this.iteratorType)}},Sl=class extends lp.HashContainer{constructor(e=[]){super();let r=this;e.forEach(function(i){r.insert(i)})}begin(){return new _r(this.p,this.h,this)}end(){return new _r(this.h,this.h,this)}rBegin(){return new _r(this._,this.h,this,1)}rEnd(){return new _r(this.h,this.h,this,1)}front(){return this.p.u}back(){return this._.u}insert(e,r){return this.M(e,void 0,r)}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;return r.u}find(e,r){let i=this.I(e,r);return new _r(i,this.h,this)}forEach(e){let r=0,i=this.p;for(;i!==this.h;)e(i.u,r++,this),i=i.B}[Symbol.iterator](){return function*(){let e=this.p;for(;e!==this.h;)yield e.u,e=e.B}.bind(this)()}},M1=Sl;Vn.default=M1});var fp=L(zn=>{"use strict";_();E();m();Object.defineProperty(zn,"t",{value:!0});zn.default=void 0;var up=vl(),N1=D1(wl()),q1=at();function D1(t){return t&&t.t?t:{default:t}}var mr=class t extends up.HashContainerIterator{constructor(e,r,i,n){super(e,r,n),this.container=i}get pointer(){this.o===this.h&&(0,q1.throwIteratorAccessError)();let e=this;return new Proxy([],{get(r,i){if(i==="0")return e.o.u;if(i==="1")return e.o.l},set(r,i,n){if(i!=="1")throw new TypeError("props must be 1");return e.o.l=n,!0}})}copy(){return new t(this.o,this.h,this.container,this.iteratorType)}},Al=class extends up.HashContainer{constructor(e=[]){super();let r=this;e.forEach(function(i){r.setElement(i[0],i[1])})}begin(){return new mr(this.p,this.h,this)}end(){return new mr(this.h,this.h,this)}rBegin(){return new mr(this._,this.h,this,1)}rEnd(){return new mr(this.h,this.h,this,1)}front(){if(this.i!==0)return[this.p.u,this.p.l]}back(){if(this.i!==0)return[this._.u,this._.l]}setElement(e,r,i){return this.M(e,r,i)}getElementByKey(e,r){if(r===void 0&&(r=(0,N1.default)(e)),r){let n=e[this.HASH_TAG];return n!==void 0?this.H[n].l:void 0}let i=this.g[e];return i?i.l:void 0}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;return[r.u,r.l]}find(e,r){let i=this.I(e,r);return new mr(i,this.h,this)}forEach(e){let r=0,i=this.p;for(;i!==this.h;)e([i.u,i.l],r++,this),i=i.B}[Symbol.iterator](){return function*(){let e=this.p;for(;e!==this.h;)yield[e.u,e.l],e=e.B}.bind(this)()}},j1=Al;zn.default=j1});var cp=L(je=>{"use strict";_();E();m();Object.defineProperty(je,"t",{value:!0});Object.defineProperty(je,"Deque",{enumerable:!0,get:function(){return z1.default}});Object.defineProperty(je,"HashMap",{enumerable:!0,get:function(){return Y1.default}});Object.defineProperty(je,"HashSet",{enumerable:!0,get:function(){return G1.default}});Object.defineProperty(je,"LinkList",{enumerable:!0,get:function(){return V1.default}});Object.defineProperty(je,"OrderedMap",{enumerable:!0,get:function(){return Q1.default}});Object.defineProperty(je,"OrderedSet",{enumerable:!0,get:function(){return K1.default}});Object.defineProperty(je,"PriorityQueue",{enumerable:!0,get:function(){return $1.default}});Object.defineProperty(je,"Queue",{enumerable:!0,get:function(){return W1.default}});Object.defineProperty(je,"Stack",{enumerable:!0,get:function(){return F1.default}});Object.defineProperty(je,"Vector",{enumerable:!0,get:function(){return H1.default}});var F1=ut(Kd()),W1=ut(Qd()),$1=ut(Gd()),H1=ut(Yd()),V1=ut(Jd()),z1=ut(Xd()),K1=ut(ip()),Q1=ut(sp()),G1=ut(ap()),Y1=ut(fp());function ut(t){return t&&t.t?t:{default:t}}});var dp=L((FO,hp)=>{"use strict";_();E();m();var J1=cp().OrderedSet,ft=ot()("number-allocator:trace"),X1=ot()("number-allocator:error");function Te(t,e){this.low=t,this.high=e}Te.prototype.equals=function(t){return this.low===t.low&&this.high===t.high};Te.prototype.compare=function(t){return this.low<t.low&&this.high<t.low?-1:t.low<this.low&&t.high<this.low?1:0};function ct(t,e){if(!(this instanceof ct))return new ct(t,e);this.min=t,this.max=e,this.ss=new J1([],(r,i)=>r.compare(i)),ft("Create"),this.clear()}ct.prototype.firstVacant=function(){return this.ss.size()===0?null:this.ss.front().low};ct.prototype.alloc=function(){if(this.ss.size()===0)return ft("alloc():empty"),null;let t=this.ss.begin(),e=t.pointer.low,r=t.pointer.high,i=e;return i+1<=r?this.ss.updateKeyByIterator(t,new Te(e+1,r)):this.ss.eraseElementByPos(0),ft("alloc():"+i),i};ct.prototype.use=function(t){let e=new Te(t,t),r=this.ss.lowerBound(e);if(!r.equals(this.ss.end())){let i=r.pointer.low,n=r.pointer.high;return r.pointer.equals(e)?(this.ss.eraseElementByIterator(r),ft("use():"+t),!0):i>t?!1:i===t?(this.ss.updateKeyByIterator(r,new Te(i+1,n)),ft("use():"+t),!0):n===t?(this.ss.updateKeyByIterator(r,new Te(i,n-1)),ft("use():"+t),!0):(this.ss.updateKeyByIterator(r,new Te(t+1,n)),this.ss.insert(new Te(i,t-1)),ft("use():"+t),!0)}return ft("use():failed"),!1};ct.prototype.free=function(t){if(t<this.min||t>this.max){X1("free():"+t+" is out of range");return}let e=new Te(t,t),r=this.ss.upperBound(e);if(r.equals(this.ss.end())){if(r.equals(this.ss.begin())){this.ss.insert(e);return}r.pre();let i=r.pointer.high;r.pointer.high+1===t?this.ss.updateKeyByIterator(r,new Te(i,t)):this.ss.insert(e)}else if(r.equals(this.ss.begin()))if(t+1===r.pointer.low){let i=r.pointer.high;this.ss.updateKeyByIterator(r,new Te(t,i))}else this.ss.insert(e);else{let i=r.pointer.low,n=r.pointer.high;r.pre();let o=r.pointer.low;r.pointer.high+1===t?t+1===i?(this.ss.eraseElementByIterator(r),this.ss.updateKeyByIterator(r,new Te(o,n))):this.ss.updateKeyByIterator(r,new Te(o,t)):t+1===i?(this.ss.eraseElementByIterator(r.next()),this.ss.insert(new Te(t,n))):this.ss.insert(e)}ft("free():"+t)};ct.prototype.clear=function(){ft("clear()"),this.ss.clear(),this.ss.insert(new Te(this.min,this.max))};ct.prototype.intervalCount=function(){return this.ss.size()};ct.prototype.dump=function(){console.log("length:"+this.ss.size());for(let t of this.ss)console.log(t)};hp.exports=ct});var Il=L((QO,pp)=>{_();E();m();var Z1=dp();pp.exports.NumberAllocator=Z1});var gp=L(Rl=>{"use strict";_();E();m();Object.defineProperty(Rl,"__esModule",{value:!0});var eE=zd(),tE=Il(),Tl=class{constructor(e){e>0&&(this.aliasToTopic=new eE.LRUCache({max:e}),this.topicToAlias={},this.numberAllocator=new tE.NumberAllocator(1,e),this.max=e,this.length=0)}put(e,r){if(r===0||r>this.max)return!1;let i=this.aliasToTopic.get(r);return i&&delete this.topicToAlias[i],this.aliasToTopic.set(r,e),this.topicToAlias[e]=r,this.numberAllocator.use(r),this.length=this.aliasToTopic.size,!0}getTopicByAlias(e){return this.aliasToTopic.get(e)}getAliasByTopic(e){let r=this.topicToAlias[e];return typeof r<"u"&&this.aliasToTopic.get(r),r}clear(){this.aliasToTopic.clear(),this.topicToAlias={},this.numberAllocator.clear(),this.length=0}getLruAlias(){let e=this.numberAllocator.firstVacant();return e||[...this.aliasToTopic.keys()][this.aliasToTopic.size-1]}};Rl.default=Tl});var yp=L(Ii=>{"use strict";_();E();m();var rE=Ii&&Ii.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Ii,"__esModule",{value:!0});var iE=vi(),nE=rE(gp()),sE=Ei(),oE=(t,e)=>{t.log("_handleConnack");let{options:r}=t,n=r.protocolVersion===5?e.reasonCode:e.returnCode;if(clearTimeout(t.connackTimer),delete t.topicAliasSend,e.properties){if(e.properties.topicAliasMaximum){if(e.properties.topicAliasMaximum>65535){t.emit("error",new Error("topicAliasMaximum from broker is out of range"));return}e.properties.topicAliasMaximum>0&&(t.topicAliasSend=new nE.default(e.properties.topicAliasMaximum))}e.properties.serverKeepAlive&&r.keepalive&&(r.keepalive=e.properties.serverKeepAlive,t._shiftPingInterval()),e.properties.maximumPacketSize&&(r.properties||(r.properties={}),r.properties.maximumPacketSize=e.properties.maximumPacketSize)}if(n===0)t.reconnecting=!1,t._onConnect(e);else if(n>0){let o=new sE.ErrorWithReasonCode(`Connection refused: ${iE.ReasonCodes[n]}`,n);t.emit("error",o)}};Ii.default=oE});var bp=L(Cl=>{"use strict";_();E();m();Object.defineProperty(Cl,"__esModule",{value:!0});var lE=(t,e,r)=>{t.log("handling pubrel packet");let i=typeof r<"u"?r:t.noop,{messageId:n}=e,o={cmd:"pubcomp",messageId:n};t.incomingStore.get(e,(s,l)=>{s?t._sendPacket(o,i):(t.emit("message",l.topic,l.payload,l),t.handleMessage(l,u=>{if(u)return i(u);t.incomingStore.del(l,t.noop),t._sendPacket(o,i)}))})};Cl.default=lE});var wp=L(Ti=>{"use strict";_();E();m();var Ri=Ti&&Ti.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Ti,"__esModule",{value:!0});var aE=Ri(Dd()),uE=Ri(Fd()),fE=Ri(yp()),cE=Ri(vi()),hE=Ri(bp()),dE=(t,e,r)=>{let{options:i}=t;if(i.protocolVersion===5&&i.properties&&i.properties.maximumPacketSize&&i.properties.maximumPacketSize<e.length)return t.emit("error",new Error(`exceeding packets size ${e.cmd}`)),t.end({reasonCode:149,properties:{reasonString:"Maximum packet size was exceeded"}}),t;switch(t.log("_handlePacket :: emitting packetreceive"),t.emit("packetreceive",e),e.cmd){case"publish":(0,aE.default)(t,e,r);break;case"puback":case"pubrec":case"pubcomp":case"suback":case"unsuback":(0,cE.default)(t,e),r();break;case"pubrel":(0,hE.default)(t,e,r);break;case"connack":(0,fE.default)(t,e),r();break;case"auth":(0,uE.default)(t,e),r();break;case"pingresp":t.pingResp=!0,r();break;case"disconnect":t.emit("disconnect",e),r();break;default:t.log("_handlePacket :: unknown command"),r();break}};Ti.default=dE});var _p=L(Jr=>{"use strict";_();E();m();var pE=Jr&&Jr.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Jr,"__esModule",{value:!0});Jr.TypedEventEmitter=void 0;var gE=pE((er(),Z(Zt))),yE=Ei(),Kn=class{};Jr.TypedEventEmitter=Kn;(0,yE.applyMixin)(Kn,gE.default)});var Gn=L(Ge=>{"use strict";_();E();m();var bE=Ge&&Ge.__createBinding||(Object.create?function(t,e,r,i){i===void 0&&(i=r);var n=Object.getOwnPropertyDescriptor(e,r);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,i,n)}:function(t,e,r,i){i===void 0&&(i=r),t[i]=e[r]}),wE=Ge&&Ge.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),_E=Ge&&Ge.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var r in t)r!=="default"&&Object.prototype.hasOwnProperty.call(t,r)&&bE(e,t,r);return wE(e,t),e},$t=Ge&&Ge.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Ge,"__esModule",{value:!0});var mE=$t(ju()),Bl=$t(Bd()),EE=$t(Wo()),vE=qt(),SE=$t(xd()),mp=$t(Ud()),Ep=_E(Nd()),AE=$t(ot()),Qn=$t(Vo()),IE=$t(wp()),TE=_p(),xl=C?C.nextTick:t=>{setTimeout(t,0)},Pl=window.setImmediate||((...t)=>{let e=t.shift();xl(()=>{e(...t)})}),vp={keepalive:60,reschedulePings:!0,protocolId:"MQTT",protocolVersion:4,reconnectPeriod:1e3,connectTimeout:30*1e3,clean:!0,resubscribe:!0,writeCache:!0},RE=["ECONNREFUSED","EADDRINUSE","ECONNRESET","ENOTFOUND","ETIMEDOUT"],Ol=class t extends TE.TypedEventEmitter{static defaultId(){return`mqttjs_${Math.random().toString(16).substr(2,8)}`}constructor(e,r){super(),this.options=r||{};for(let i in vp)typeof this.options[i]>"u"?this.options[i]=vp[i]:this.options[i]=r[i];this.log=this.options.log||(0,AE.default)("mqttjs:client"),this.noop=this._noop.bind(this),this.log("MqttClient :: options.protocol",r.protocol),this.log("MqttClient :: options.protocolVersion",r.protocolVersion),this.log("MqttClient :: options.username",r.username),this.log("MqttClient :: options.keepalive",r.keepalive),this.log("MqttClient :: options.reconnectPeriod",r.reconnectPeriod),this.log("MqttClient :: options.rejectUnauthorized",r.rejectUnauthorized),this.log("MqttClient :: options.properties.topicAliasMaximum",r.properties?r.properties.topicAliasMaximum:void 0),this.options.clientId=typeof r.clientId=="string"?r.clientId:t.defaultId(),this.log("MqttClient :: clientId",this.options.clientId),this.options.customHandleAcks=r.protocolVersion===5&&r.customHandleAcks?r.customHandleAcks:(...i)=>{i[3](null,0)},this.options.writeCache||(Bl.default.writeToStream.cacheNumbers=!1),this.streamBuilder=e,this.messageIdProvider=typeof this.options.messageIdProvider>"u"?new EE.default:this.options.messageIdProvider,this.outgoingStore=r.outgoingStore||new Qn.default,this.incomingStore=r.incomingStore||new Qn.default,this.queueQoSZero=r.queueQoSZero===void 0?!0:r.queueQoSZero,this._resubscribeTopics={},this.messageIdToTopic={},this.pingTimer=null,this.connected=!1,this.disconnecting=!1,this.reconnecting=!1,this.queue=[],this.connackTimer=null,this.reconnectTimer=null,this._storeProcessing=!1,this._packetIdsDuringStoreProcessing={},this._storeProcessingQueue=[],this.outgoing={},this._firstConnection=!0,r.properties&&r.properties.topicAliasMaximum>0&&(r.properties.topicAliasMaximum>65535?this.log("MqttClient :: options.properties.topicAliasMaximum is out of range"):this.topicAliasRecv=new mE.default(r.properties.topicAliasMaximum)),this.on("connect",()=>{let{queue:i}=this,n=()=>{let o=i.shift();this.log("deliver :: entry %o",o);let s=null;if(!o){this._resubscribe();return}s=o.packet,this.log("deliver :: call _sendPacket for %o",s);let l=!0;s.messageId&&s.messageId!==0&&(this.messageIdProvider.register(s.messageId)||(l=!1)),l?this._sendPacket(s,u=>{o.cb&&o.cb(u),n()}):(this.log("messageId: %d has already used. The message is skipped and removed.",s.messageId),n())};this.log("connect :: sending queued packets"),n()}),this.on("close",()=>{this.log("close :: connected set to `false`"),this.connected=!1,this.log("close :: clearing connackTimer"),clearTimeout(this.connackTimer),this.log("close :: clearing ping timer"),this.pingTimer!==null&&(this.pingTimer.clear(),this.pingTimer=null),this.topicAliasRecv&&this.topicAliasRecv.clear(),this.log("close :: calling _setupReconnect"),this._setupReconnect()}),this.options.manualConnect||(this.log("MqttClient :: setting up stream"),this.connect())}handleAuth(e,r){r()}handleMessage(e,r){r()}_nextId(){return this.messageIdProvider.allocate()}getLastMessageId(){return this.messageIdProvider.getLastAllocated()}connect(){var e;let r=new vE.Writable,i=Bl.default.parser(this.options),n=null,o=[];this.log("connect :: calling method to clear reconnect"),this._clearReconnect(),this.log("connect :: using streamBuilder provided to client to create stream"),this.stream=this.streamBuilder(this),i.on("packet",h=>{this.log("parser :: on packet push to packets array."),o.push(h)});let s=()=>{this.log("work :: getting next packet in queue");let h=o.shift();if(h)this.log("work :: packet pulled from queue"),(0,IE.default)(this,h,l);else{this.log("work :: no packets in queue");let d=n;n=null,this.log("work :: done flag is %s",!!d),d&&d()}},l=()=>{if(o.length)xl(s);else{let h=n;n=null,h()}};r._write=(h,d,g)=>{n=g,this.log("writable stream :: parsing buffer"),i.parse(h),s()};let u=h=>{this.log("streamErrorHandler :: error",h.message),RE.includes(h.code)?(this.log("streamErrorHandler :: emitting error"),this.emit("error",h)):this.noop(h)};this.log("connect :: pipe stream to writable stream"),this.stream.pipe(r),this.stream.on("error",u),this.stream.on("close",()=>{this.log("(%s)stream :: on close",this.options.clientId),this._flushVolatile(),this.log("stream: emit close to MqttClient"),this.emit("close")}),this.log("connect: sending packet `connect`");let c={cmd:"connect",protocolId:this.options.protocolId,protocolVersion:this.options.protocolVersion,clean:this.options.clean,clientId:this.options.clientId,keepalive:this.options.keepalive,username:this.options.username,password:this.options.password,properties:this.options.properties};if(this.options.will&&(c.will=Object.assign(Object.assign({},this.options.will),{payload:(e=this.options.will)===null||e===void 0?void 0:e.payload})),this.topicAliasRecv&&(c.properties||(c.properties={}),this.topicAliasRecv&&(c.properties.topicAliasMaximum=this.topicAliasRecv.max)),this._writePacket(c),i.on("error",this.emit.bind(this,"error")),this.options.properties){if(!this.options.properties.authenticationMethod&&this.options.properties.authenticationData)return this.end(()=>this.emit("error",new Error("Packet has no Authentication Method"))),this;if(this.options.properties.authenticationMethod&&this.options.authPacket&&typeof this.options.authPacket=="object"){let h=Object.assign({cmd:"auth",reasonCode:0},this.options.authPacket);this._writePacket(h)}}return this.stream.setMaxListeners(1e3),clearTimeout(this.connackTimer),this.connackTimer=setTimeout(()=>{this.log("!!connectTimeout hit!! Calling _cleanUp with force `true`"),this._cleanUp(!0)},this.options.connectTimeout),this}publish(e,r,i,n){this.log("publish :: message `%s` to topic `%s`",r,e);let{options:o}=this;typeof i=="function"&&(n=i,i=null),i=i||{},i=Object.assign(Object.assign({},{qos:0,retain:!1,dup:!1}),i);let{qos:l,retain:u,dup:c,properties:h,cbStorePut:d}=i;if(this._checkDisconnecting(n))return this;let g=()=>{let y=0;if((l===1||l===2)&&(y=this._nextId(),y===null))return this.log("No messageId left"),!1;let w={cmd:"publish",topic:e,payload:r,qos:l,retain:u,messageId:y,dup:c};switch(o.protocolVersion===5&&(w.properties=h),this.log("publish :: qos",l),l){case 1:case 2:this.outgoing[w.messageId]={volatile:!1,cb:n||this.noop},this.log("MqttClient:publish: packet cmd: %s",w.cmd),this._sendPacket(w,void 0,d);break;default:this.log("MqttClient:publish: packet cmd: %s",w.cmd),this._sendPacket(w,n,d);break}return!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!g())&&this._storeProcessingQueue.push({invoke:g,cbStorePut:i.cbStorePut,callback:n}),this}publishAsync(e,r,i){return new Promise((n,o)=>{this.publish(e,r,i,(s,l)=>{s?o(s):n(l)})})}subscribe(e,r,i){let n=this.options.protocolVersion;typeof r=="function"&&(i=r),i=i||this.noop;let o=!1,s=[];typeof e=="string"?(e=[e],s=e):Array.isArray(e)?s=e:typeof e=="object"&&(o=e.resubscribe,delete e.resubscribe,s=Object.keys(e));let l=Ep.validateTopics(s);if(l!==null)return Pl(i,new Error(`Invalid topic ${l}`)),this;if(this._checkDisconnecting(i))return this.log("subscribe: discconecting true"),this;let u={qos:0};n===5&&(u.nl=!1,u.rap=!1,u.rh=0),r=Object.assign(Object.assign({},u),r);let c=r.properties,h=[],d=(y,w)=>{if(w=w||r,!Object.prototype.hasOwnProperty.call(this._resubscribeTopics,y)||this._resubscribeTopics[y].qos<w.qos||o){let S={topic:y,qos:w.qos};n===5&&(S.nl=w.nl,S.rap=w.rap,S.rh=w.rh,S.properties=c),this.log("subscribe: pushing topic `%s` and qos `%s` to subs list",S.topic,S.qos),h.push(S)}};if(Array.isArray(e)?e.forEach(y=>{this.log("subscribe: array topic %s",y),d(y)}):Object.keys(e).forEach(y=>{this.log("subscribe: object topic %s, %o",y,e[y]),d(y,e[y])}),!h.length)return i(null,[]),this;let g=()=>{let y=this._nextId();if(y===null)return this.log("No messageId left"),!1;let w={cmd:"subscribe",subscriptions:h,messageId:y};if(c&&(w.properties=c),this.options.resubscribe){this.log("subscribe :: resubscribe true");let S=[];h.forEach(A=>{if(this.options.reconnectPeriod>0){let I={qos:A.qos};n===5&&(I.nl=A.nl||!1,I.rap=A.rap||!1,I.rh=A.rh||0,I.properties=A.properties),this._resubscribeTopics[A.topic]=I,S.push(A.topic)}}),this.messageIdToTopic[w.messageId]=S}return this.outgoing[w.messageId]={volatile:!0,cb(S,A){if(!S){let{granted:I}=A;for(let P=0;P<I.length;P+=1)h[P].qos=I[P]}i(S,h)}},this.log("subscribe :: call _sendPacket"),this._sendPacket(w),!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!g())&&this._storeProcessingQueue.push({invoke:g,callback:i}),this}subscribeAsync(e,r){return new Promise((i,n)=>{this.subscribe(e,r,(o,s)=>{o?n(o):i(s)})})}unsubscribe(e,r,i){typeof e=="string"&&(e=[e]),typeof r=="function"&&(i=r),i=i||this.noop;let n=Ep.validateTopics(e);if(n!==null)return Pl(i,new Error(`Invalid topic ${n}`)),this;if(this._checkDisconnecting(i))return this;let o=()=>{let s=this._nextId();if(s===null)return this.log("No messageId left"),!1;let l={cmd:"unsubscribe",messageId:s,unsubscriptions:[]};return typeof e=="string"?l.unsubscriptions=[e]:Array.isArray(e)&&(l.unsubscriptions=e),this.options.resubscribe&&l.unsubscriptions.forEach(u=>{delete this._resubscribeTopics[u]}),typeof r=="object"&&r.properties&&(l.properties=r.properties),this.outgoing[l.messageId]={volatile:!0,cb:i},this.log("unsubscribe: call _sendPacket"),this._sendPacket(l),!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!o())&&this._storeProcessingQueue.push({invoke:o,callback:i}),this}unsubscribeAsync(e,r){return new Promise((i,n)=>{this.unsubscribe(e,r,(o,s)=>{o?n(o):i(s)})})}end(e,r,i){this.log("end :: (%s)",this.options.clientId),(e==null||typeof e!="boolean")&&(i=i||r,r=e,e=!1),typeof r!="object"&&(i=i||r,r=null),this.log("end :: cb? %s",!!i),(!i||typeof i!="function")&&(i=this.noop);let n=()=>{this.log("end :: closeStores: closing incoming and outgoing stores"),this.disconnected=!0,this.incomingStore.close(s=>{this.outgoingStore.close(l=>{if(this.log("end :: closeStores: emitting end"),this.emit("end"),i){let u=s||l;this.log("end :: closeStores: invoking callback with args"),i(u)}})}),this._deferredReconnect&&this._deferredReconnect()},o=()=>{this.log("end :: (%s) :: finish :: calling _cleanUp with force %s",this.options.clientId,e),this._cleanUp(e,()=>{this.log("end :: finish :: calling process.nextTick on closeStores"),xl(n)},r)};return this.disconnecting?(i(),this):(this._clearReconnect(),this.disconnecting=!0,!e&&Object.keys(this.outgoing).length>0?(this.log("end :: (%s) :: calling finish in 10ms once outgoing is empty",this.options.clientId),this.once("outgoingEmpty",setTimeout.bind(null,o,10))):(this.log("end :: (%s) :: immediately calling finish",this.options.clientId),o()),this)}endAsync(e,r){return new Promise((i,n)=>{this.end(e,r,o=>{o?n(o):i()})})}removeOutgoingMessage(e){if(this.outgoing[e]){let{cb:r}=this.outgoing[e];this._removeOutgoingAndStoreMessage(e,()=>{r(new Error("Message removed"))})}return this}reconnect(e){this.log("client reconnect");let r=()=>{e?(this.options.incomingStore=e.incomingStore,this.options.outgoingStore=e.outgoingStore):(this.options.incomingStore=null,this.options.outgoingStore=null),this.incomingStore=this.options.incomingStore||new Qn.default,this.outgoingStore=this.options.outgoingStore||new Qn.default,this.disconnecting=!1,this.disconnected=!1,this._deferredReconnect=null,this._reconnect()};return this.disconnecting&&!this.disconnected?this._deferredReconnect=r:r(),this}_flushVolatile(){this.outgoing&&(this.log("_flushVolatile :: deleting volatile messages from the queue and setting their callbacks as error function"),Object.keys(this.outgoing).forEach(e=>{this.outgoing[e].volatile&&typeof this.outgoing[e].cb=="function"&&(this.outgoing[e].cb(new Error("Connection closed")),delete this.outgoing[e])}))}_flush(){this.outgoing&&(this.log("_flush: queue exists? %b",!!this.outgoing),Object.keys(this.outgoing).forEach(e=>{typeof this.outgoing[e].cb=="function"&&(this.outgoing[e].cb(new Error("Connection closed")),delete this.outgoing[e])}))}_removeTopicAliasAndRecoverTopicName(e){let r;e.properties&&(r=e.properties.topicAlias);let i=e.topic.toString();if(this.log("_removeTopicAliasAndRecoverTopicName :: alias %d, topic %o",r,i),i.length===0){if(typeof r>"u")return new Error("Unregistered Topic Alias");if(i=this.topicAliasSend.getTopicByAlias(r),typeof i>"u")return new Error("Unregistered Topic Alias");e.topic=i}r&&delete e.properties.topicAlias}_checkDisconnecting(e){return this.disconnecting&&(e&&e!==this.noop?e(new Error("client disconnecting")):this.emit("error",new Error("client disconnecting"))),this.disconnecting}_reconnect(){this.log("_reconnect: emitting reconnect to client"),this.emit("reconnect"),this.connected?(this.end(()=>{this.connect()}),this.log("client already connected. disconnecting first.")):(this.log("_reconnect: calling connect"),this.connect())}_setupReconnect(){!this.disconnecting&&!this.reconnectTimer&&this.options.reconnectPeriod>0?(this.reconnecting||(this.log("_setupReconnect :: emit `offline` state"),this.emit("offline"),this.log("_setupReconnect :: set `reconnecting` to `true`"),this.reconnecting=!0),this.log("_setupReconnect :: setting reconnectTimer for %d ms",this.options.reconnectPeriod),this.reconnectTimer=setInterval(()=>{this.log("reconnectTimer :: reconnect triggered!"),this._reconnect()},this.options.reconnectPeriod)):this.log("_setupReconnect :: doing nothing...")}_clearReconnect(){this.log("_clearReconnect : clearing reconnect timer"),this.reconnectTimer&&(clearInterval(this.reconnectTimer),this.reconnectTimer=null)}_cleanUp(e,r,i={}){if(r&&(this.log("_cleanUp :: done callback provided for on stream close"),this.stream.on("close",r)),this.log("_cleanUp :: forced? %s",e),e)this.options.reconnectPeriod===0&&this.options.clean&&this._flush(),this.log("_cleanUp :: (%s) :: destroying stream",this.options.clientId),this.stream.destroy();else{let n=Object.assign({cmd:"disconnect"},i);this.log("_cleanUp :: (%s) :: call _sendPacket with disconnect packet",this.options.clientId),this._sendPacket(n,()=>{this.log("_cleanUp :: (%s) :: destroying stream",this.options.clientId),Pl(()=>{this.stream.end(()=>{this.log("_cleanUp :: (%s) :: stream destroyed",this.options.clientId)})})})}this.disconnecting||(this.log("_cleanUp :: client not disconnecting. Clearing and resetting reconnect."),this._clearReconnect(),this._setupReconnect()),this.pingTimer!==null&&(this.log("_cleanUp :: clearing pingTimer"),this.pingTimer.clear(),this.pingTimer=null),r&&!this.connected&&(this.log("_cleanUp :: (%s) :: removing stream `done` callback `close` listener",this.options.clientId),this.stream.removeListener("close",r),r())}_storeAndSend(e,r,i){this.log("storeAndSend :: store packet with cmd %s to outgoingStore",e.cmd);let n=e,o;if(n.cmd==="publish"&&(n=(0,mp.default)(e),o=this._removeTopicAliasAndRecoverTopicName(n),o))return r&&r(o);this.outgoingStore.put(n,s=>{if(s)return r&&r(s);i(),this._writePacket(e,r)})}_applyTopicAlias(e){if(this.options.protocolVersion===5&&e.cmd==="publish"){let r;e.properties&&(r=e.properties.topicAlias);let i=e.topic.toString();if(this.topicAliasSend)if(r){if(i.length!==0&&(this.log("applyTopicAlias :: register topic: %s - alias: %d",i,r),!this.topicAliasSend.put(i,r)))return this.log("applyTopicAlias :: error out of range. topic: %s - alias: %d",i,r),new Error("Sending Topic Alias out of range")}else i.length!==0&&(this.options.autoAssignTopicAlias?(r=this.topicAliasSend.getAliasByTopic(i),r?(e.topic="",e.properties=Object.assign(Object.assign({},e.properties),{topicAlias:r}),this.log("applyTopicAlias :: auto assign(use) topic: %s - alias: %d",i,r)):(r=this.topicAliasSend.getLruAlias(),this.topicAliasSend.put(i,r),e.properties=Object.assign(Object.assign({},e.properties),{topicAlias:r}),this.log("applyTopicAlias :: auto assign topic: %s - alias: %d",i,r))):this.options.autoUseTopicAlias&&(r=this.topicAliasSend.getAliasByTopic(i),r&&(e.topic="",e.properties=Object.assign(Object.assign({},e.properties),{topicAlias:r}),this.log("applyTopicAlias :: auto use topic: %s - alias: %d",i,r))));else if(r)return this.log("applyTopicAlias :: error out of range. topic: %s - alias: %d",i,r),new Error("Sending Topic Alias out of range")}}_noop(e){this.log("noop ::",e)}_writePacket(e,r){this.log("_writePacket :: packet: %O",e),this.log("_writePacket :: emitting `packetsend`"),this.emit("packetsend",e),this._shiftPingInterval(),this.log("_writePacket :: writing to stream");let i=Bl.default.writeToStream(e,this.stream,this.options);this.log("_writePacket :: writeToStream result %s",i),!i&&r&&r!==this.noop?(this.log("_writePacket :: handle events on `drain` once through callback."),this.stream.once("drain",r)):r&&(this.log("_writePacket :: invoking cb"),r())}_sendPacket(e,r,i,n){this.log("_sendPacket :: (%s) ::  start",this.options.clientId),i=i||this.noop,r=r||this.noop;let o=this._applyTopicAlias(e);if(o){r(o);return}if(!this.connected){if(e.cmd==="auth"){this._writePacket(e,r);return}this.log("_sendPacket :: client not connected. Storing packet offline."),this._storePacket(e,r,i);return}if(n){this._writePacket(e,r);return}switch(e.cmd){case"publish":break;case"pubrel":this._storeAndSend(e,r,i);return;default:this._writePacket(e,r);return}switch(e.qos){case 2:case 1:this._storeAndSend(e,r,i);break;case 0:default:this._writePacket(e,r);break}this.log("_sendPacket :: (%s) ::  end",this.options.clientId)}_storePacket(e,r,i){this.log("_storePacket :: packet: %o",e),this.log("_storePacket :: cb? %s",!!r),i=i||this.noop;let n=e;if(n.cmd==="publish"){n=(0,mp.default)(e);let s=this._removeTopicAliasAndRecoverTopicName(n);if(s)return r&&r(s)}let o=n.qos||0;o===0&&this.queueQoSZero||n.cmd!=="publish"?this.queue.push({packet:n,cb:r}):o>0?(r=this.outgoing[n.messageId]?this.outgoing[n.messageId].cb:null,this.outgoingStore.put(n,s=>{if(s)return r&&r(s);i()})):r&&r(new Error("No connection to broker"))}_setupPingTimer(){this.log("_setupPingTimer :: keepalive %d (seconds)",this.options.keepalive),!this.pingTimer&&this.options.keepalive&&(this.pingResp=!0,this.pingTimer=(0,SE.default)(()=>{this._checkPing()},this.options.keepalive*1e3))}_shiftPingInterval(){this.pingTimer&&this.options.keepalive&&this.options.reschedulePings&&this.pingTimer.reschedule(this.options.keepalive*1e3)}_checkPing(){this.log("_checkPing :: checking ping..."),this.pingResp?(this.log("_checkPing :: ping response received. Clearing flag and sending `pingreq`"),this.pingResp=!1,this._sendPacket({cmd:"pingreq"})):(this.log("_checkPing :: calling _cleanUp with force true"),this._cleanUp(!0))}_resubscribe(){this.log("_resubscribe");let e=Object.keys(this._resubscribeTopics);if(!this._firstConnection&&(this.options.clean||this.options.protocolVersion>=4&&!this.connackPacket.sessionPresent)&&e.length>0)if(this.options.resubscribe)if(this.options.protocolVersion===5){this.log("_resubscribe: protocolVersion 5");for(let r=0;r<e.length;r++){let i={};i[e[r]]=this._resubscribeTopics[e[r]],i.resubscribe=!0,this.subscribe(i,{properties:i[e[r]].properties})}}else this._resubscribeTopics.resubscribe=!0,this.subscribe(this._resubscribeTopics);else this._resubscribeTopics={};this._firstConnection=!1}_onConnect(e){if(this.disconnected){this.emit("connect",e);return}this.connackPacket=e,this.messageIdProvider.clear(),this._setupPingTimer(),this.connected=!0;let r=()=>{let i=this.outgoingStore.createStream(),n=()=>{i.destroy(),i=null,this._flushStoreProcessingQueue(),o()},o=()=>{this._storeProcessing=!1,this._packetIdsDuringStoreProcessing={}};this.once("close",n),i.on("error",l=>{o(),this._flushStoreProcessingQueue(),this.removeListener("close",n),this.emit("error",l)});let s=()=>{if(!i)return;let l=i.read(1),u;if(!l){i.once("readable",s);return}if(this._storeProcessing=!0,this._packetIdsDuringStoreProcessing[l.messageId]){s();return}!this.disconnecting&&!this.reconnectTimer?(u=this.outgoing[l.messageId]?this.outgoing[l.messageId].cb:null,this.outgoing[l.messageId]={volatile:!1,cb(c,h){u&&u(c,h),s()}},this._packetIdsDuringStoreProcessing[l.messageId]=!0,this.messageIdProvider.register(l.messageId)?this._sendPacket(l,void 0,void 0,!0):this.log("messageId: %d has already used.",l.messageId)):i.destroy&&i.destroy()};i.on("end",()=>{let l=!0;for(let u in this._packetIdsDuringStoreProcessing)if(!this._packetIdsDuringStoreProcessing[u]){l=!1;break}l?(o(),this.removeListener("close",n),this._invokeAllStoreProcessingQueue(),this.emit("connect",e)):r()}),s()};r()}_invokeStoreProcessingQueue(){if(!this._storeProcessing&&this._storeProcessingQueue.length>0){let e=this._storeProcessingQueue[0];if(e&&e.invoke())return this._storeProcessingQueue.shift(),!0}return!1}_invokeAllStoreProcessingQueue(){for(;this._invokeStoreProcessingQueue(););}_flushStoreProcessingQueue(){for(let e of this._storeProcessingQueue)e.cbStorePut&&e.cbStorePut(new Error("Connection closed")),e.callback&&e.callback(new Error("Connection closed"));this._storeProcessingQueue.splice(0)}_removeOutgoingAndStoreMessage(e,r){delete this.outgoing[e],this.outgoingStore.del({messageId:e},(i,n)=>{r(i,n),this.messageIdProvider.deallocate(e),this._invokeStoreProcessingQueue()})}};Ge.default=Ol});var Sp=L(Ll=>{"use strict";_();E();m();Object.defineProperty(Ll,"__esModule",{value:!0});var CE=Il(),kl=class{constructor(){this.numberAllocator=new CE.NumberAllocator(1,65535)}allocate(){return this.lastId=this.numberAllocator.alloc(),this.lastId}getLastAllocated(){return this.lastId}register(e){return this.numberAllocator.use(e)}deallocate(e){this.numberAllocator.free(e)}clear(){this.numberAllocator.clear()}};Ll.default=kl});function Er(t){throw new RangeError(OE[t])}function Ap(t,e){let r=t.split("@"),i="";r.length>1&&(i=r[0]+"@",t=r[1]);let n=function(o,s){let l=[],u=o.length;for(;u--;)l[u]=s(o[u]);return l}((t=t.replace(xE,".")).split("."),e).join(".");return i+n}function Cp(t){let e=[],r=0,i=t.length;for(;r<i;){let n=t.charCodeAt(r++);if(n>=55296&&n<=56319&&r<i){let o=t.charCodeAt(r++);(64512&o)==56320?e.push(((1023&n)<<10)+(1023&o)+65536):(e.push(n),r--)}else e.push(n)}return e}var BE,PE,xE,OE,ht,Ul,Ip,Bp,Tp,Rp,Ht,Pp=ge(()=>{_();E();m();BE=/^xn--/,PE=/[^\0-\x7E]/,xE=/[\x2E\u3002\uFF0E\uFF61]/g,OE={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},ht=Math.floor,Ul=String.fromCharCode;Ip=function(t,e){return t+22+75*(t<26)-((e!=0)<<5)},Bp=function(t,e,r){let i=0;for(t=r?ht(t/700):t>>1,t+=ht(t/e);t>455;i+=36)t=ht(t/35);return ht(i+36*t/(t+38))},Tp=function(t){let e=[],r=t.length,i=0,n=128,o=72,s=t.lastIndexOf("-");s<0&&(s=0);for(let u=0;u<s;++u)t.charCodeAt(u)>=128&&Er("not-basic"),e.push(t.charCodeAt(u));for(let u=s>0?s+1:0;u<r;){let c=i;for(let d=1,g=36;;g+=36){u>=r&&Er("invalid-input");let y=(l=t.charCodeAt(u++))-48<10?l-22:l-65<26?l-65:l-97<26?l-97:36;(y>=36||y>ht((2147483647-i)/d))&&Er("overflow"),i+=y*d;let w=g<=o?1:g>=o+26?26:g-o;if(y<w)break;let S=36-w;d>ht(2147483647/S)&&Er("overflow"),d*=S}let h=e.length+1;o=Bp(i-c,h,c==0),ht(i/h)>2147483647-n&&Er("overflow"),n+=ht(i/h),i%=h,e.splice(i++,0,n)}var l;return String.fromCodePoint(...e)},Rp=function(t){let e=[],r=(t=Cp(t)).length,i=128,n=0,o=72;for(let u of t)u<128&&e.push(Ul(u));let s=e.length,l=s;for(s&&e.push("-");l<r;){let u=2147483647;for(let h of t)h>=i&&h<u&&(u=h);let c=l+1;u-i>ht((2147483647-n)/c)&&Er("overflow"),n+=(u-i)*c,i=u;for(let h of t)if(h<i&&++n>2147483647&&Er("overflow"),h==i){let d=n;for(let g=36;;g+=36){let y=g<=o?1:g>=o+26?26:g-o;if(d<y)break;let w=d-y,S=36-y;e.push(Ul(Ip(y+w%S,0))),d=ht(w/S)}e.push(Ul(Ip(d,0))),o=Bp(n,c,l==s),n=0,++l}++n,++i}return e.join("")},Ht={version:"2.1.0",ucs2:{decode:Cp,encode:t=>String.fromCodePoint(...t)},decode:Tp,encode:Rp,toASCII:function(t){return Ap(t,function(e){return PE.test(e)?"xn--"+Rp(e):e})},toUnicode:function(t){return Ap(t,function(e){return BE.test(e)?Tp(e.slice(4).toLowerCase()):e})}};Ht.decode;Ht.encode;Ht.toASCII;Ht.toUnicode;Ht.ucs2;Ht.version});function kE(t,e){return Object.prototype.hasOwnProperty.call(t,e)}var LE,Ci,UE,dt,xp=ge(()=>{_();E();m();LE=function(t,e,r,i){e=e||"&",r=r||"=";var n={};if(typeof t!="string"||t.length===0)return n;var o=/\+/g;t=t.split(e);var s=1e3;i&&typeof i.maxKeys=="number"&&(s=i.maxKeys);var l=t.length;s>0&&l>s&&(l=s);for(var u=0;u<l;++u){var c,h,d,g,y=t[u].replace(o,"%20"),w=y.indexOf(r);w>=0?(c=y.substr(0,w),h=y.substr(w+1)):(c=y,h=""),d=decodeURIComponent(c),g=decodeURIComponent(h),kE(n,d)?Array.isArray(n[d])?n[d].push(g):n[d]=[n[d],g]:n[d]=g}return n},Ci=function(t){switch(typeof t){case"string":return t;case"boolean":return t?"true":"false";case"number":return isFinite(t)?t:"";default:return""}},UE=function(t,e,r,i){return e=e||"&",r=r||"=",t===null&&(t=void 0),typeof t=="object"?Object.keys(t).map(function(n){var o=encodeURIComponent(Ci(n))+r;return Array.isArray(t[n])?t[n].map(function(s){return o+encodeURIComponent(Ci(s))}).join(e):o+encodeURIComponent(Ci(t[n]))}).join(e):i?encodeURIComponent(Ci(i))+r+encodeURIComponent(Ci(t)):""},dt={};dt.decode=dt.parse=LE,dt.encode=dt.stringify=UE;dt.decode;dt.encode;dt.parse;dt.stringify});function Ml(){throw new Error("setTimeout has not been defined")}function Nl(){throw new Error("clearTimeout has not been defined")}function Lp(t){if(Ct===setTimeout)return setTimeout(t,0);if((Ct===Ml||!Ct)&&setTimeout)return Ct=setTimeout,setTimeout(t,0);try{return Ct(t,0)}catch{try{return Ct.call(null,t,0)}catch{return Ct.call(this||Zr,t,0)}}}function ME(){Xr&&vr&&(Xr=!1,vr.length?Pt=vr.concat(Pt):Yn=-1,Pt.length&&Up())}function Up(){if(!Xr){var t=Lp(ME);Xr=!0;for(var e=Pt.length;e;){for(vr=Pt,Pt=[];++Yn<e;)vr&&vr[Yn].run();Yn=-1,e=Pt.length}vr=null,Xr=!1,function(r){if(Bt===clearTimeout)return clearTimeout(r);if((Bt===Nl||!Bt)&&clearTimeout)return Bt=clearTimeout,clearTimeout(r);try{Bt(r)}catch{try{return Bt.call(null,r)}catch{return Bt.call(this||Zr,r)}}}(t)}}function Op(t,e){(this||Zr).fun=t,(this||Zr).array=e}function Rt(){}var kp,Ct,Bt,Zr,fe,vr,Pt,Xr,Yn,ne,Mp=ge(()=>{_();E();m();Zr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:window,fe=kp={};(function(){try{Ct=typeof setTimeout=="function"?setTimeout:Ml}catch{Ct=Ml}try{Bt=typeof clearTimeout=="function"?clearTimeout:Nl}catch{Bt=Nl}})();Pt=[],Xr=!1,Yn=-1;fe.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];Pt.push(new Op(t,e)),Pt.length!==1||Xr||Lp(Up)},Op.prototype.run=function(){(this||Zr).fun.apply(null,(this||Zr).array)},fe.title="browser",fe.browser=!0,fe.env={},fe.argv=[],fe.version="",fe.versions={},fe.on=Rt,fe.addListener=Rt,fe.once=Rt,fe.off=Rt,fe.removeListener=Rt,fe.removeAllListeners=Rt,fe.emit=Rt,fe.prependListener=Rt,fe.prependOnceListener=Rt,fe.listeners=function(t){return[]},fe.binding=function(t){throw new Error("process.binding is not supported")},fe.cwd=function(){return"/"},fe.chdir=function(t){throw new Error("process.chdir is not supported")},fe.umask=function(){return 0};ne=kp;ne.addListener;ne.argv;ne.binding;ne.browser;ne.chdir;ne.cwd;ne.emit;ne.env;ne.listeners;ne.nextTick;ne.off;ne.on;ne.once;ne.prependListener;ne.prependOnceListener;ne.removeAllListeners;ne.removeListener;ne.title;ne.umask;ne.version;ne.versions});function NE(){if(Np)return ql;Np=!0;var t=ql={},e,r;function i(){throw new Error("setTimeout has not been defined")}function n(){throw new Error("clearTimeout has not been defined")}(function(){try{typeof setTimeout=="function"?e=setTimeout:e=i}catch{e=i}try{typeof clearTimeout=="function"?r=clearTimeout:r=n}catch{r=n}})();function o(S){if(e===setTimeout)return setTimeout(S,0);if((e===i||!e)&&setTimeout)return e=setTimeout,setTimeout(S,0);try{return e(S,0)}catch{try{return e.call(null,S,0)}catch{return e.call(this||ei,S,0)}}}function s(S){if(r===clearTimeout)return clearTimeout(S);if((r===n||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(S);try{return r(S)}catch{try{return r.call(null,S)}catch{return r.call(this||ei,S)}}}var l=[],u=!1,c,h=-1;function d(){!u||!c||(u=!1,c.length?l=c.concat(l):h=-1,l.length&&g())}function g(){if(!u){var S=o(d);u=!0;for(var A=l.length;A;){for(c=l,l=[];++h<A;)c&&c[h].run();h=-1,A=l.length}c=null,u=!1,s(S)}}t.nextTick=function(S){var A=new Array(arguments.length-1);if(arguments.length>1)for(var I=1;I<arguments.length;I++)A[I-1]=arguments[I];l.push(new y(S,A)),l.length===1&&!u&&o(g)};function y(S,A){(this||ei).fun=S,(this||ei).array=A}y.prototype.run=function(){(this||ei).fun.apply(null,(this||ei).array)},t.title="browser",t.browser=!0,t.env={},t.argv=[],t.version="",t.versions={};function w(){}return t.on=w,t.addListener=w,t.once=w,t.off=w,t.removeListener=w,t.removeAllListeners=w,t.emit=w,t.prependListener=w,t.prependOnceListener=w,t.listeners=function(S){return[]},t.binding=function(S){throw new Error("process.binding is not supported")},t.cwd=function(){return"/"},t.chdir=function(S){throw new Error("process.chdir is not supported")},t.umask=function(){return 0},ql}var ql,Np,ei,re,Dl=ge(()=>{_();E();m();ql={},Np=!1,ei=typeof globalThis<"u"?globalThis:typeof self<"u"?self:window;re=NE();re.platform="browser";re.addListener;re.argv;re.binding;re.browser;re.chdir;re.cwd;re.emit;re.env;re.listeners;re.nextTick;re.off;re.on;re.once;re.prependListener;re.prependOnceListener;re.removeAllListeners;re.removeListener;re.title;re.umask;re.version;re.versions});function qE(){if(qp)return jl;qp=!0;var t=re;function e(o){if(typeof o!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(o))}function r(o,s){for(var l="",u=0,c=-1,h=0,d,g=0;g<=o.length;++g){if(g<o.length)d=o.charCodeAt(g);else{if(d===47)break;d=47}if(d===47){if(!(c===g-1||h===1))if(c!==g-1&&h===2){if(l.length<2||u!==2||l.charCodeAt(l.length-1)!==46||l.charCodeAt(l.length-2)!==46){if(l.length>2){var y=l.lastIndexOf("/");if(y!==l.length-1){y===-1?(l="",u=0):(l=l.slice(0,y),u=l.length-1-l.lastIndexOf("/")),c=g,h=0;continue}}else if(l.length===2||l.length===1){l="",u=0,c=g,h=0;continue}}s&&(l.length>0?l+="/..":l="..",u=2)}else l.length>0?l+="/"+o.slice(c+1,g):l=o.slice(c+1,g),u=g-c-1;c=g,h=0}else d===46&&h!==-1?++h:h=-1}return l}function i(o,s){var l=s.dir||s.root,u=s.base||(s.name||"")+(s.ext||"");return l?l===s.root?l+u:l+o+u:u}var n={resolve:function(){for(var s="",l=!1,u,c=arguments.length-1;c>=-1&&!l;c--){var h;c>=0?h=arguments[c]:(u===void 0&&(u=t.cwd()),h=u),e(h),h.length!==0&&(s=h+"/"+s,l=h.charCodeAt(0)===47)}return s=r(s,!l),l?s.length>0?"/"+s:"/":s.length>0?s:"."},normalize:function(s){if(e(s),s.length===0)return".";var l=s.charCodeAt(0)===47,u=s.charCodeAt(s.length-1)===47;return s=r(s,!l),s.length===0&&!l&&(s="."),s.length>0&&u&&(s+="/"),l?"/"+s:s},isAbsolute:function(s){return e(s),s.length>0&&s.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var s,l=0;l<arguments.length;++l){var u=arguments[l];e(u),u.length>0&&(s===void 0?s=u:s+="/"+u)}return s===void 0?".":n.normalize(s)},relative:function(s,l){if(e(s),e(l),s===l||(s=n.resolve(s),l=n.resolve(l),s===l))return"";for(var u=1;u<s.length&&s.charCodeAt(u)===47;++u);for(var c=s.length,h=c-u,d=1;d<l.length&&l.charCodeAt(d)===47;++d);for(var g=l.length,y=g-d,w=h<y?h:y,S=-1,A=0;A<=w;++A){if(A===w){if(y>w){if(l.charCodeAt(d+A)===47)return l.slice(d+A+1);if(A===0)return l.slice(d+A)}else h>w&&(s.charCodeAt(u+A)===47?S=A:A===0&&(S=0));break}var I=s.charCodeAt(u+A),P=l.charCodeAt(d+A);if(I!==P)break;I===47&&(S=A)}var R="";for(A=u+S+1;A<=c;++A)(A===c||s.charCodeAt(A)===47)&&(R.length===0?R+="..":R+="/..");return R.length>0?R+l.slice(d+S):(d+=S,l.charCodeAt(d)===47&&++d,l.slice(d))},_makeLong:function(s){return s},dirname:function(s){if(e(s),s.length===0)return".";for(var l=s.charCodeAt(0),u=l===47,c=-1,h=!0,d=s.length-1;d>=1;--d)if(l=s.charCodeAt(d),l===47){if(!h){c=d;break}}else h=!1;return c===-1?u?"/":".":u&&c===1?"//":s.slice(0,c)},basename:function(s,l){if(l!==void 0&&typeof l!="string")throw new TypeError('"ext" argument must be a string');e(s);var u=0,c=-1,h=!0,d;if(l!==void 0&&l.length>0&&l.length<=s.length){if(l.length===s.length&&l===s)return"";var g=l.length-1,y=-1;for(d=s.length-1;d>=0;--d){var w=s.charCodeAt(d);if(w===47){if(!h){u=d+1;break}}else y===-1&&(h=!1,y=d+1),g>=0&&(w===l.charCodeAt(g)?--g===-1&&(c=d):(g=-1,c=y))}return u===c?c=y:c===-1&&(c=s.length),s.slice(u,c)}else{for(d=s.length-1;d>=0;--d)if(s.charCodeAt(d)===47){if(!h){u=d+1;break}}else c===-1&&(h=!1,c=d+1);return c===-1?"":s.slice(u,c)}},extname:function(s){e(s);for(var l=-1,u=0,c=-1,h=!0,d=0,g=s.length-1;g>=0;--g){var y=s.charCodeAt(g);if(y===47){if(!h){u=g+1;break}continue}c===-1&&(h=!1,c=g+1),y===46?l===-1?l=g:d!==1&&(d=1):l!==-1&&(d=-1)}return l===-1||c===-1||d===0||d===1&&l===c-1&&l===u+1?"":s.slice(l,c)},format:function(s){if(s===null||typeof s!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof s);return i("/",s)},parse:function(s){e(s);var l={root:"",dir:"",base:"",ext:"",name:""};if(s.length===0)return l;var u=s.charCodeAt(0),c=u===47,h;c?(l.root="/",h=1):h=0;for(var d=-1,g=0,y=-1,w=!0,S=s.length-1,A=0;S>=h;--S){if(u=s.charCodeAt(S),u===47){if(!w){g=S+1;break}continue}y===-1&&(w=!1,y=S+1),u===46?d===-1?d=S:A!==1&&(A=1):d!==-1&&(A=-1)}return d===-1||y===-1||A===0||A===1&&d===y-1&&d===g+1?y!==-1&&(g===0&&c?l.base=l.name=s.slice(1,y):l.base=l.name=s.slice(g,y)):(g===0&&c?(l.name=s.slice(1,d),l.base=s.slice(1,y)):(l.name=s.slice(g,d),l.base=s.slice(g,y)),l.ext=s.slice(d,y)),g>0?l.dir=s.slice(0,g-1):c&&(l.dir="/"),l},sep:"/",delimiter:":",win32:null,posix:null};return n.posix=n,jl=n,jl}var jl,qp,Fl,Dp=ge(()=>{_();E();m();Dl();jl={},qp=!1;Fl=qE()});var Kp={};zt(Kp,{URL:()=>pv,Url:()=>uv,default:()=>X,fileURLToPath:()=>Vp,format:()=>fv,parse:()=>dv,pathToFileURL:()=>zp,resolve:()=>cv,resolveObject:()=>hv});function Fe(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}function Bi(t,e,r){if(t&&pt.isObject(t)&&t instanceof Fe)return t;var i=new Fe;return i.parse(t,e,r),i}function zE(){if($p)return Hl;$p=!0;var t=ne;function e(o){if(typeof o!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(o))}function r(o,s){for(var l="",u=0,c=-1,h=0,d,g=0;g<=o.length;++g){if(g<o.length)d=o.charCodeAt(g);else{if(d===47)break;d=47}if(d===47){if(!(c===g-1||h===1))if(c!==g-1&&h===2){if(l.length<2||u!==2||l.charCodeAt(l.length-1)!==46||l.charCodeAt(l.length-2)!==46){if(l.length>2){var y=l.lastIndexOf("/");if(y!==l.length-1){y===-1?(l="",u=0):(l=l.slice(0,y),u=l.length-1-l.lastIndexOf("/")),c=g,h=0;continue}}else if(l.length===2||l.length===1){l="",u=0,c=g,h=0;continue}}s&&(l.length>0?l+="/..":l="..",u=2)}else l.length>0?l+="/"+o.slice(c+1,g):l=o.slice(c+1,g),u=g-c-1;c=g,h=0}else d===46&&h!==-1?++h:h=-1}return l}function i(o,s){var l=s.dir||s.root,u=s.base||(s.name||"")+(s.ext||"");return l?l===s.root?l+u:l+o+u:u}var n={resolve:function(){for(var s="",l=!1,u,c=arguments.length-1;c>=-1&&!l;c--){var h;c>=0?h=arguments[c]:(u===void 0&&(u=t.cwd()),h=u),e(h),h.length!==0&&(s=h+"/"+s,l=h.charCodeAt(0)===47)}return s=r(s,!l),l?s.length>0?"/"+s:"/":s.length>0?s:"."},normalize:function(s){if(e(s),s.length===0)return".";var l=s.charCodeAt(0)===47,u=s.charCodeAt(s.length-1)===47;return s=r(s,!l),s.length===0&&!l&&(s="."),s.length>0&&u&&(s+="/"),l?"/"+s:s},isAbsolute:function(s){return e(s),s.length>0&&s.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var s,l=0;l<arguments.length;++l){var u=arguments[l];e(u),u.length>0&&(s===void 0?s=u:s+="/"+u)}return s===void 0?".":n.normalize(s)},relative:function(s,l){if(e(s),e(l),s===l||(s=n.resolve(s),l=n.resolve(l),s===l))return"";for(var u=1;u<s.length&&s.charCodeAt(u)===47;++u);for(var c=s.length,h=c-u,d=1;d<l.length&&l.charCodeAt(d)===47;++d);for(var g=l.length,y=g-d,w=h<y?h:y,S=-1,A=0;A<=w;++A){if(A===w){if(y>w){if(l.charCodeAt(d+A)===47)return l.slice(d+A+1);if(A===0)return l.slice(d+A)}else h>w&&(s.charCodeAt(u+A)===47?S=A:A===0&&(S=0));break}var I=s.charCodeAt(u+A),P=l.charCodeAt(d+A);if(I!==P)break;I===47&&(S=A)}var R="";for(A=u+S+1;A<=c;++A)(A===c||s.charCodeAt(A)===47)&&(R.length===0?R+="..":R+="/..");return R.length>0?R+l.slice(d+S):(d+=S,l.charCodeAt(d)===47&&++d,l.slice(d))},_makeLong:function(s){return s},dirname:function(s){if(e(s),s.length===0)return".";for(var l=s.charCodeAt(0),u=l===47,c=-1,h=!0,d=s.length-1;d>=1;--d)if(l=s.charCodeAt(d),l===47){if(!h){c=d;break}}else h=!1;return c===-1?u?"/":".":u&&c===1?"//":s.slice(0,c)},basename:function(s,l){if(l!==void 0&&typeof l!="string")throw new TypeError('"ext" argument must be a string');e(s);var u=0,c=-1,h=!0,d;if(l!==void 0&&l.length>0&&l.length<=s.length){if(l.length===s.length&&l===s)return"";var g=l.length-1,y=-1;for(d=s.length-1;d>=0;--d){var w=s.charCodeAt(d);if(w===47){if(!h){u=d+1;break}}else y===-1&&(h=!1,y=d+1),g>=0&&(w===l.charCodeAt(g)?--g===-1&&(c=d):(g=-1,c=y))}return u===c?c=y:c===-1&&(c=s.length),s.slice(u,c)}else{for(d=s.length-1;d>=0;--d)if(s.charCodeAt(d)===47){if(!h){u=d+1;break}}else c===-1&&(h=!1,c=d+1);return c===-1?"":s.slice(u,c)}},extname:function(s){e(s);for(var l=-1,u=0,c=-1,h=!0,d=0,g=s.length-1;g>=0;--g){var y=s.charCodeAt(g);if(y===47){if(!h){u=g+1;break}continue}c===-1&&(h=!1,c=g+1),y===46?l===-1?l=g:d!==1&&(d=1):l!==-1&&(d=-1)}return l===-1||c===-1||d===0||d===1&&l===c-1&&l===u+1?"":s.slice(l,c)},format:function(s){if(s===null||typeof s!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof s);return i("/",s)},parse:function(s){e(s);var l={root:"",dir:"",base:"",ext:"",name:""};if(s.length===0)return l;var u=s.charCodeAt(0),c=u===47,h;c?(l.root="/",h=1):h=0;for(var d=-1,g=0,y=-1,w=!0,S=s.length-1,A=0;S>=h;--S){if(u=s.charCodeAt(S),u===47){if(!w){g=S+1;break}continue}y===-1&&(w=!1,y=S+1),u===46?d===-1?d=S:A!==1&&(A=1):d!==-1&&(A=-1)}return d===-1||y===-1||A===0||A===1&&d===y-1&&d===g+1?y!==-1&&(g===0&&c?l.base=l.name=s.slice(1,y):l.base=l.name=s.slice(g,y)):(g===0&&c?(l.name=s.slice(1,d),l.base=s.slice(1,y)):(l.name=s.slice(g,d),l.base=s.slice(g,y)),l.ext=s.slice(d,y)),g>0?l.dir=s.slice(0,g-1):c&&(l.dir="/"),l},sep:"/",delimiter:":",win32:null,posix:null};return n.posix=n,Hl=n,Hl}function nv(t){if(typeof t=="string")t=new URL(t);else if(!(t instanceof URL))throw new Deno.errors.InvalidData("invalid argument path , must be a string or URL");if(t.protocol!=="file:")throw new Deno.errors.InvalidData("invalid url scheme");return zl?sv(t):ov(t)}function sv(t){let e=t.hostname,r=t.pathname;for(let i=0;i<r.length;i++)if(r[i]==="%"){let n=r.codePointAt(i+2)||32;if(r[i+1]==="2"&&n===102||r[i+1]==="5"&&n===99)throw new Deno.errors.InvalidData("must not include encoded \\ or / characters")}if(r=r.replace(XE,"\\"),r=decodeURIComponent(r),e!=="")return`\\\\${e}${r}`;{let i=r.codePointAt(1)|32,n=r[2];if(i<YE||i>JE||n!==":")throw new Deno.errors.InvalidData("file url path must be absolute");return r.slice(1)}}function ov(t){if(t.hostname!=="")throw new Deno.errors.InvalidData("invalid file url hostname");let e=t.pathname;for(let r=0;r<e.length;r++)if(e[r]==="%"){let i=e.codePointAt(r+2)||32;if(e[r+1]==="2"&&i===102)throw new Deno.errors.InvalidData("must not include encoded / characters")}return decodeURIComponent(e)}function lv(t){let e=Hp.resolve(t),r=t.charCodeAt(t.length-1);(r===GE||zl&&r===QE)&&e[e.length-1]!==Hp.sep&&(e+="/");let i=new URL("file://");return e.includes("%")&&(e=e.replace(ZE,"%25")),!zl&&e.includes("\\")&&(e=e.replace(ev,"%5C")),e.includes(`
`)&&(e=e.replace(tv,"%0A")),e.includes("\r")&&(e=e.replace(rv,"%0D")),e.includes("	")&&(e=e.replace(iv,"%09")),i.pathname=e,i}function Vp(t){if(typeof t=="string")t=new URL(t);else if(!(t instanceof URL))throw new Deno.errors.InvalidData("invalid argument path , must be a string or URL");if(t.protocol!=="file:")throw new Deno.errors.InvalidData("invalid url scheme");return Kl?Iv(t):Tv(t)}function Iv(t){let e=t.hostname,r=t.pathname;for(let i=0;i<r.length;i++)if(r[i]==="%"){let n=r.codePointAt(i+2)||32;if(r[i+1]==="2"&&n===102||r[i+1]==="5"&&n===99)throw new Deno.errors.InvalidData("must not include encoded \\ or / characters")}if(r=r.replace(_v,"\\"),r=decodeURIComponent(r),e!=="")return`\\\\${e}${r}`;{let i=r.codePointAt(1)|32,n=r[2];if(i<bv||i>wv||n!==":")throw new Deno.errors.InvalidData("file url path must be absolute");return r.slice(1)}}function Tv(t){if(t.hostname!=="")throw new Deno.errors.InvalidData("invalid file url hostname");let e=t.pathname;for(let r=0;r<e.length;r++)if(e[r]==="%"){let i=e.codePointAt(r+2)||32;if(e[r+1]==="2"&&i===102)throw new Deno.errors.InvalidData("must not include encoded / characters")}return decodeURIComponent(e)}function zp(t){let e=Fl.resolve(t),r=t.charCodeAt(t.length-1);(r===yv||Kl&&r===gv)&&e[e.length-1]!==Fl.sep&&(e+="/");let i=new URL("file://");return e.includes("%")&&(e=e.replace(mv,"%25")),!Kl&&e.includes("\\")&&(e=e.replace(Ev,"%5C")),e.includes(`
`)&&(e=e.replace(vv,"%0A")),e.includes("\r")&&(e=e.replace(Sv,"%0D")),e.includes("	")&&(e=e.replace(Av,"%09")),i.pathname=e,i}var X,DE,pt,jE,FE,WE,$E,Vl,jp,Fp,Wp,HE,VE,Wl,ti,$l,Hl,$p,Hp,KE,QE,GE,YE,JE,zl,XE,ZE,ev,tv,rv,iv,av,uv,fv,cv,hv,dv,pv,gv,yv,bv,wv,Kl,_v,mv,Ev,vv,Sv,Av,Qp=ge(()=>{_();E();m();Pp();xp();Mp();Dp();Dl();X={},DE=Ht,pt={isString:function(t){return typeof t=="string"},isObject:function(t){return typeof t=="object"&&t!==null},isNull:function(t){return t===null},isNullOrUndefined:function(t){return t==null}};X.parse=Bi,X.resolve=function(t,e){return Bi(t,!1,!0).resolve(e)},X.resolveObject=function(t,e){return t?Bi(t,!1,!0).resolveObject(e):e},X.format=function(t){return pt.isString(t)&&(t=Bi(t)),t instanceof Fe?t.format():Fe.prototype.format.call(t)},X.Url=Fe;jE=/^([a-z0-9.+-]+:)/i,FE=/:[0-9]*$/,WE=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,$E=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r",`
`,"	"]),Vl=["'"].concat($E),jp=["%","/","?",";","#"].concat(Vl),Fp=["/","?","#"],Wp=/^[+a-z0-9A-Z_-]{0,63}$/,HE=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,VE={javascript:!0,"javascript:":!0},Wl={javascript:!0,"javascript:":!0},ti={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},$l=dt;Fe.prototype.parse=function(t,e,r){if(!pt.isString(t))throw new TypeError("Parameter 'url' must be a string, not "+typeof t);var i=t.indexOf("?"),n=i!==-1&&i<t.indexOf("#")?"?":"#",o=t.split(n);o[0]=o[0].replace(/\\/g,"/");var s=t=o.join(n);if(s=s.trim(),!r&&t.split("#").length===1){var l=WE.exec(s);if(l)return this.path=s,this.href=s,this.pathname=l[1],l[2]?(this.search=l[2],this.query=e?$l.parse(this.search.substr(1)):this.search.substr(1)):e&&(this.search="",this.query={}),this}var u=jE.exec(s);if(u){var c=(u=u[0]).toLowerCase();this.protocol=c,s=s.substr(u.length)}if(r||u||s.match(/^\/\/[^@\/]+@[^@\/]+/)){var h=s.substr(0,2)==="//";!h||u&&Wl[u]||(s=s.substr(2),this.slashes=!0)}if(!Wl[u]&&(h||u&&!ti[u])){for(var d,g,y=-1,w=0;w<Fp.length;w++)(S=s.indexOf(Fp[w]))!==-1&&(y===-1||S<y)&&(y=S);for((g=y===-1?s.lastIndexOf("@"):s.lastIndexOf("@",y))!==-1&&(d=s.slice(0,g),s=s.slice(g+1),this.auth=decodeURIComponent(d)),y=-1,w=0;w<jp.length;w++){var S;(S=s.indexOf(jp[w]))!==-1&&(y===-1||S<y)&&(y=S)}y===-1&&(y=s.length),this.host=s.slice(0,y),s=s.slice(y),this.parseHost(),this.hostname=this.hostname||"";var A=this.hostname[0]==="["&&this.hostname[this.hostname.length-1]==="]";if(!A)for(var I=this.hostname.split(/\./),P=(w=0,I.length);w<P;w++){var R=I[w];if(R&&!R.match(Wp)){for(var M="",N=0,V=R.length;N<V;N++)R.charCodeAt(N)>127?M+="x":M+=R[N];if(!M.match(Wp)){var Q=I.slice(0,w),z=I.slice(w+1),Y=R.match(HE);Y&&(Q.push(Y[1]),z.unshift(Y[2])),z.length&&(s="/"+z.join(".")+s),this.hostname=Q.join(".");break}}}this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),A||(this.hostname=DE.toASCII(this.hostname));var ve=this.port?":"+this.port:"",ni=this.hostname||"";this.host=ni+ve,this.href+=this.host,A&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),s[0]!=="/"&&(s="/"+s))}if(!VE[c])for(w=0,P=Vl.length;w<P;w++){var we=Vl[w];if(s.indexOf(we)!==-1){var Ar=encodeURIComponent(we);Ar===we&&(Ar=escape(we)),s=s.split(we).join(Ar)}}var Ir=s.indexOf("#");Ir!==-1&&(this.hash=s.substr(Ir),s=s.slice(0,Ir));var Tr=s.indexOf("?");if(Tr!==-1?(this.search=s.substr(Tr),this.query=s.substr(Tr+1),e&&(this.query=$l.parse(this.query)),s=s.slice(0,Tr)):e&&(this.search="",this.query={}),s&&(this.pathname=s),ti[c]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){ve=this.pathname||"";var ts=this.search||"";this.path=ve+ts}return this.href=this.format(),this},Fe.prototype.format=function(){var t=this.auth||"";t&&(t=(t=encodeURIComponent(t)).replace(/%3A/i,":"),t+="@");var e=this.protocol||"",r=this.pathname||"",i=this.hash||"",n=!1,o="";this.host?n=t+this.host:this.hostname&&(n=t+(this.hostname.indexOf(":")===-1?this.hostname:"["+this.hostname+"]"),this.port&&(n+=":"+this.port)),this.query&&pt.isObject(this.query)&&Object.keys(this.query).length&&(o=$l.stringify(this.query));var s=this.search||o&&"?"+o||"";return e&&e.substr(-1)!==":"&&(e+=":"),this.slashes||(!e||ti[e])&&n!==!1?(n="//"+(n||""),r&&r.charAt(0)!=="/"&&(r="/"+r)):n||(n=""),i&&i.charAt(0)!=="#"&&(i="#"+i),s&&s.charAt(0)!=="?"&&(s="?"+s),e+n+(r=r.replace(/[?#]/g,function(l){return encodeURIComponent(l)}))+(s=s.replace("#","%23"))+i},Fe.prototype.resolve=function(t){return this.resolveObject(Bi(t,!1,!0)).format()},Fe.prototype.resolveObject=function(t){if(pt.isString(t)){var e=new Fe;e.parse(t,!1,!0),t=e}for(var r=new Fe,i=Object.keys(this),n=0;n<i.length;n++){var o=i[n];r[o]=this[o]}if(r.hash=t.hash,t.href==="")return r.href=r.format(),r;if(t.slashes&&!t.protocol){for(var s=Object.keys(t),l=0;l<s.length;l++){var u=s[l];u!=="protocol"&&(r[u]=t[u])}return ti[r.protocol]&&r.hostname&&!r.pathname&&(r.path=r.pathname="/"),r.href=r.format(),r}if(t.protocol&&t.protocol!==r.protocol){if(!ti[t.protocol]){for(var c=Object.keys(t),h=0;h<c.length;h++){var d=c[h];r[d]=t[d]}return r.href=r.format(),r}if(r.protocol=t.protocol,t.host||Wl[t.protocol])r.pathname=t.pathname;else{for(var g=(t.pathname||"").split("/");g.length&&!(t.host=g.shift()););t.host||(t.host=""),t.hostname||(t.hostname=""),g[0]!==""&&g.unshift(""),g.length<2&&g.unshift(""),r.pathname=g.join("/")}if(r.search=t.search,r.query=t.query,r.host=t.host||"",r.auth=t.auth,r.hostname=t.hostname||t.host,r.port=t.port,r.pathname||r.search){var y=r.pathname||"",w=r.search||"";r.path=y+w}return r.slashes=r.slashes||t.slashes,r.href=r.format(),r}var S=r.pathname&&r.pathname.charAt(0)==="/",A=t.host||t.pathname&&t.pathname.charAt(0)==="/",I=A||S||r.host&&t.pathname,P=I,R=r.pathname&&r.pathname.split("/")||[],M=(g=t.pathname&&t.pathname.split("/")||[],r.protocol&&!ti[r.protocol]);if(M&&(r.hostname="",r.port=null,r.host&&(R[0]===""?R[0]=r.host:R.unshift(r.host)),r.host="",t.protocol&&(t.hostname=null,t.port=null,t.host&&(g[0]===""?g[0]=t.host:g.unshift(t.host)),t.host=null),I=I&&(g[0]===""||R[0]==="")),A)r.host=t.host||t.host===""?t.host:r.host,r.hostname=t.hostname||t.hostname===""?t.hostname:r.hostname,r.search=t.search,r.query=t.query,R=g;else if(g.length)R||(R=[]),R.pop(),R=R.concat(g),r.search=t.search,r.query=t.query;else if(!pt.isNullOrUndefined(t.search))return M&&(r.hostname=r.host=R.shift(),(Y=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=Y.shift(),r.host=r.hostname=Y.shift())),r.search=t.search,r.query=t.query,pt.isNull(r.pathname)&&pt.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r;if(!R.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var N=R.slice(-1)[0],V=(r.host||t.host||R.length>1)&&(N==="."||N==="..")||N==="",Q=0,z=R.length;z>=0;z--)(N=R[z])==="."?R.splice(z,1):N===".."?(R.splice(z,1),Q++):Q&&(R.splice(z,1),Q--);if(!I&&!P)for(;Q--;Q)R.unshift("..");!I||R[0]===""||R[0]&&R[0].charAt(0)==="/"||R.unshift(""),V&&R.join("/").substr(-1)!=="/"&&R.push("");var Y,ve=R[0]===""||R[0]&&R[0].charAt(0)==="/";return M&&(r.hostname=r.host=ve?"":R.length?R.shift():"",(Y=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=Y.shift(),r.host=r.hostname=Y.shift())),(I=I||r.host&&R.length)&&!ve&&R.unshift(""),R.length?r.pathname=R.join("/"):(r.pathname=null,r.path=null),pt.isNull(r.pathname)&&pt.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=t.auth||r.auth,r.slashes=r.slashes||t.slashes,r.href=r.format(),r},Fe.prototype.parseHost=function(){var t=this.host,e=FE.exec(t);e&&((e=e[0])!==":"&&(this.port=e.substr(1)),t=t.substr(0,t.length-e.length)),t&&(this.hostname=t)};X.Url;X.format;X.resolve;X.resolveObject;Hl={},$p=!1;Hp=zE(),KE=typeof Deno<"u"?Deno.build.os==="windows"?"win32":Deno.build.os:void 0;X.URL=typeof URL<"u"?URL:null;X.pathToFileURL=lv;X.fileURLToPath=nv;X.Url;X.format;X.resolve;X.resolveObject;X.URL;QE=92,GE=47,YE=97,JE=122,zl=KE==="win32",XE=/\//g,ZE=/%/g,ev=/\\/g,tv=/\n/g,rv=/\r/g,iv=/\t/g;av=typeof Deno<"u"?Deno.build.os==="windows"?"win32":Deno.build.os:void 0;X.URL=typeof URL<"u"?URL:null;X.pathToFileURL=zp;X.fileURLToPath=Vp;uv=X.Url,fv=X.format,cv=X.resolve,hv=X.resolveObject,dv=X.parse,pv=X.URL,gv=92,yv=47,bv=97,wv=122,Kl=av==="win32",_v=/\//g,mv=/%/g,Ev=/\\/g,vv=/\n/g,Sv=/\r/g,Av=/\t/g});var Gl=L(Ql=>{"use strict";_();E();m();Object.defineProperty(Ql,"__esModule",{value:!0});var Rv=typeof window<"u"&&typeof window.document<"u"||typeof self=="object"&&self.constructor&&self.constructor.name==="DedicatedWorkerGlobalScope"||typeof navigator<"u"&&navigator.product==="ReactNative";Ql.default=Rv});var Yl={};zt(Yl,{Server:()=>Le,Socket:()=>Le,Stream:()=>Le,_createServerHandle:()=>Le,_normalizeArgs:()=>Le,_setSimultaneousAccepts:()=>Le,connect:()=>Le,createConnection:()=>Le,createServer:()=>Le,default:()=>Cv,isIP:()=>Le,isIPv4:()=>Le,isIPv6:()=>Le});function Le(){throw new Error("Node.js net module is not supported by JSPM core outside of Node.js")}var Cv,Jl=ge(()=>{_();E();m();Cv={_createServerHandle:Le,_normalizeArgs:Le,_setSimultaneousAccepts:Le,connect:Le,createConnection:Le,createServer:Le,isIP:Le,isIPv4:Le,isIPv6:Le,Server:Le,Socket:Le,Stream:Le}});var Xl=L(Pi=>{"use strict";_();E();m();var Gp=Pi&&Pi.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Pi,"__esModule",{value:!0});var Bv=Gp((Jl(),Z(Yl))),Pv=Gp(ot()),xv=(0,Pv.default)("mqttjs:tcp"),Ov=(t,e)=>{e.port=e.port||1883,e.hostname=e.hostname||e.host||"localhost";let{port:r}=e,i=e.hostname;return xv("port %d and host %s",r,i),Bv.default.createConnection(r,i)};Pi.default=Ov});var Yp={};zt(Yp,{default:()=>kv});var kv,Jp=ge(()=>{_();E();m();kv={}});var ea=L(xi=>{"use strict";_();E();m();var Zl=xi&&xi.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(xi,"__esModule",{value:!0});var Lv=Zl((Jp(),Z(Yp))),Uv=Zl((Jl(),Z(Yl))),Mv=Zl(ot()),Nv=(0,Mv.default)("mqttjs:tls"),qv=(t,e)=>{e.port=e.port||8883,e.host=e.hostname||e.host||"localhost",Uv.default.isIP(e.host)===0&&(e.servername=e.host),e.rejectUnauthorized=e.rejectUnauthorized!==!1,delete e.path,Nv("port %d host %s rejectUnauthorized %b",e.port,e.host,e.rejectUnauthorized);let r=Lv.default.connect(e);r.on("secureConnect",()=>{e.rejectUnauthorized&&!r.authorized?r.emit("error",new Error("TLS not authorized")):r.removeListener("error",i)});function i(n){e.rejectUnauthorized&&t.emit("error",n),r.end()}return r.on("error",i),r};xi.default=qv});var Jn=L(ri=>{"use strict";_();E();m();Object.defineProperty(ri,"__esModule",{value:!0});ri.BufferedDuplex=ri.writev=void 0;var Dv=qt();function Xp(t,e){let r=new Array(t.length);for(let i=0;i<t.length;i++)typeof t[i].chunk=="string"?r[i]=k.from(t[i].chunk,"utf8"):r[i]=t[i].chunk;this._write(k.concat(r),"binary",e)}ri.writev=Xp;var ta=class extends Dv.Duplex{constructor(e,r,i){super({objectMode:!0}),this.proxy=r,this.socket=i,this.writeQueue=[],e.objectMode||(this._writev=Xp.bind(this)),this.isSocketOpen=!1,this.proxy.on("data",n=>{this.push(n)})}_read(e){this.proxy.read(e)}_write(e,r,i){this.isSocketOpen?this.writeToProxy(e,r,i):this.writeQueue.push({chunk:e,encoding:r,cb:i})}_final(e){this.writeQueue=[],this.proxy.end(e)}socketReady(){this.emit("connect"),this.isSocketOpen=!0,this.processWriteQueue()}writeToProxy(e,r,i){this.proxy.write(e,r)===!1?this.proxy.once("drain",i):i()}processWriteQueue(){for(;this.writeQueue.length>0;){let{chunk:e,encoding:r,cb:i}=this.writeQueue.shift();this.writeToProxy(e,r,i)}}};ri.BufferedDuplex=ta});var na=L(ia=>{"use strict";_();E();m();Object.defineProperty(ia,"__esModule",{value:!0});var Zp=(ye(),Z(me)),jv=qt(),Fv=Jn(),gt,ra,Ue;function Wv(){let t=new jv.Transform;return t._write=(e,r,i)=>{gt.send({data:e.buffer,success(){i()},fail(n){i(new Error(n))}})},t._flush=e=>{gt.close({success(){e()}})},t}function $v(t){t.hostname||(t.hostname="localhost"),t.path||(t.path="/"),t.wsOptions||(t.wsOptions={})}function Hv(t,e){let r=t.protocol==="wxs"?"wss":"ws",i=`${r}://${t.hostname}${t.path}`;return t.port&&t.port!==80&&t.port!==443&&(i=`${r}://${t.hostname}:${t.port}${t.path}`),typeof t.transformWsUrl=="function"&&(i=t.transformWsUrl(i,t,e)),i}function Vv(){gt.onOpen(()=>{Ue.socketReady()}),gt.onMessage(t=>{let{data:e}=t;e instanceof ArrayBuffer?e=Zp.Buffer.from(e):e=Zp.Buffer.from(e,"utf8"),ra.push(e)}),gt.onClose(()=>{Ue.emit("close"),Ue.end(),Ue.destroy()}),gt.onError(t=>{let e=new Error(t.errMsg);Ue.destroy(e)})}var zv=(t,e)=>{if(e.hostname=e.hostname||e.host,!e.hostname)throw new Error("Could not determine host. Specify host manually.");let r=e.protocolId==="MQIsdp"&&e.protocolVersion===3?"mqttv3.1":"mqtt";$v(e);let i=Hv(e,t);gt=wx.connectSocket({url:i,protocols:[r]}),ra=Wv(),Ue=new Fv.BufferedDuplex(e,ra,gt),Ue._destroy=(o,s)=>{gt.close({success(){s&&s(o)}})};let n=Ue.destroy;return Ue.destroy=(o,s)=>(Ue.destroy=n,setTimeout(()=>{gt.close({fail(){Ue._destroy(o,s)}})},0),Ue),Vv(),Ue};ia.default=zv});var la=L(oa=>{"use strict";_();E();m();Object.defineProperty(oa,"__esModule",{value:!0});var sa=(ye(),Z(me)),Kv=qt(),Qv=Jn(),xt,Xn,ii,eg=!1;function Gv(){let t=new Kv.Transform;return t._write=(e,r,i)=>{xt.sendSocketMessage({data:e.buffer,success(){i()},fail(){i(new Error)}})},t._flush=e=>{xt.closeSocket({success(){e()}})},t}function Yv(t){t.hostname||(t.hostname="localhost"),t.path||(t.path="/"),t.wsOptions||(t.wsOptions={})}function Jv(t,e){let r=t.protocol==="alis"?"wss":"ws",i=`${r}://${t.hostname}${t.path}`;return t.port&&t.port!==80&&t.port!==443&&(i=`${r}://${t.hostname}:${t.port}${t.path}`),typeof t.transformWsUrl=="function"&&(i=t.transformWsUrl(i,t,e)),i}function Xv(){eg||(eg=!0,xt.onSocketOpen(()=>{ii.socketReady()}),xt.onSocketMessage(t=>{if(typeof t.data=="string"){let e=sa.Buffer.from(t.data,"base64");Xn.push(e)}else{let e=new FileReader;e.addEventListener("load",()=>{let r=e.result;r instanceof ArrayBuffer?r=sa.Buffer.from(r):r=sa.Buffer.from(r,"utf8"),Xn.push(r)}),e.readAsArrayBuffer(t.data)}}),xt.onSocketClose(()=>{ii.end(),ii.destroy()}),xt.onSocketError(t=>{ii.destroy(t)}))}var Zv=(t,e)=>{if(e.hostname=e.hostname||e.host,!e.hostname)throw new Error("Could not determine host. Specify host manually.");let r=e.protocolId==="MQIsdp"&&e.protocolVersion===3?"mqttv3.1":"mqtt";Yv(e);let i=Jv(e,t);return xt=e.my,xt.connectSocket({url:i,protocols:r}),Xn=Gv(),ii=new Qv.BufferedDuplex(e,Xn,xt),Xv(),ii};oa.default=Zv});var rg=L(($U,tg)=>{"use strict";_();E();m();tg.exports=function(){throw new Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}});var ca=L(Oi=>{"use strict";_();E();m();var fa=Oi&&Oi.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Oi,"__esModule",{value:!0});var aa=(ye(),Z(me)),ig=fa(rg()),eS=fa(ot()),tS=qt(),ng=fa(Gl()),ua=Jn(),Vt=(0,eS.default)("mqttjs:ws"),rS=["rejectUnauthorized","ca","cert","key","pfx","passphrase"];function sg(t,e){let r=`${t.protocol}://${t.hostname}:${t.port}${t.path}`;return typeof t.transformWsUrl=="function"&&(r=t.transformWsUrl(r,t,e)),r}function og(t){let e=t;return t.hostname||(e.hostname="localhost"),t.port||(t.protocol==="wss"?e.port=443:e.port=80),t.path||(e.path="/"),t.wsOptions||(e.wsOptions={}),!ng.default&&t.protocol==="wss"&&rS.forEach(r=>{Object.prototype.hasOwnProperty.call(t,r)&&!Object.prototype.hasOwnProperty.call(t.wsOptions,r)&&(e.wsOptions[r]=t[r])}),e}function iS(t){let e=og(t);if(e.hostname||(e.hostname=e.host),!e.hostname){if(typeof document>"u")throw new Error("Could not determine host. Specify host manually.");let r=new URL(document.URL);e.hostname=r.hostname,e.port||(e.port=Number(r.port))}return e.objectMode===void 0&&(e.objectMode=!(e.binary===!0||e.binary===void 0)),e}function nS(t,e,r){Vt("createWebSocket"),Vt(`protocol: ${r.protocolId} ${r.protocolVersion}`);let i=r.protocolId==="MQIsdp"&&r.protocolVersion===3?"mqttv3.1":"mqtt";Vt(`creating new Websocket for url: ${e} and protocol: ${i}`);let n;return r.createWebsocket?n=r.createWebsocket(e,[i],r):n=new ig.default(e,[i],r.wsOptions),n}function sS(t,e){let r=e.protocolId==="MQIsdp"&&e.protocolVersion===3?"mqttv3.1":"mqtt",i=sg(e,t),n;return e.createWebsocket?n=e.createWebsocket(i,[r],e):n=new WebSocket(i,[r]),n.binaryType="arraybuffer",n}var oS=(t,e)=>{Vt("streamBuilder");let r=og(e),i=sg(r,t),n=nS(t,i,r),o=ig.default.createWebSocketStream(n,r.wsOptions);return o.url=i,n.on("close",()=>{o.destroy()}),o},lS=(t,e)=>{Vt("browserStreamBuilder");let r,n=iS(e).browserBufferSize||1024*512,o=e.browserBufferTimeout||1e3,s=!e.objectMode,l=sS(t,e),u=h(e,S,A);e.objectMode||(u._writev=ua.writev.bind(u)),u.on("close",()=>{l.close()});let c=typeof l.addEventListener<"u";l.readyState===l.OPEN?(r=u,r.socket=l):(r=new ua.BufferedDuplex(e,u,l),c?l.addEventListener("open",d):l.onopen=d),c?(l.addEventListener("close",g),l.addEventListener("error",y),l.addEventListener("message",w)):(l.onclose=g,l.onerror=y,l.onmessage=w);function h(I,P,R){let M=new tS.Transform({objectMode:I.objectMode});return M._write=P,M._flush=R,M}function d(){Vt("WebSocket onOpen"),r instanceof ua.BufferedDuplex&&r.socketReady()}function g(I){Vt("WebSocket onClose",I),r.end(),r.destroy()}function y(I){Vt("WebSocket onError",I);let P=new Error("WebSocket error");P.event=I,r.destroy(P)}function w(I){let{data:P}=I;P instanceof ArrayBuffer?P=aa.Buffer.from(P):P=aa.Buffer.from(P,"utf8"),u.push(P)}function S(I,P,R){if(l.bufferedAmount>n){setTimeout(S,o,I,P,R);return}s&&typeof I=="string"&&(I=aa.Buffer.from(I,"utf8"));try{l.send(I)}catch(M){return R(M)}R()}function A(I){l.close(),I()}return r};Oi.default=ng.default?lS:oS});var ug=L(Sr=>{"use strict";_();E();m();var Zn=Sr&&Sr.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Sr,"__esModule",{value:!0});Sr.connectAsync=void 0;var aS=Zn(ot()),uS=Zn((Qp(),Z(Kp))),fS=Zn(Gn()),cS=Zn(Gl()),lg=(0,aS.default)("mqttjs"),Re={};cS.default?(Re.wx=na().default,Re.wxs=na().default,Re.ali=la().default,Re.alis=la().default):(Re.mqtt=Xl().default,Re.tcp=Xl().default,Re.ssl=ea().default,Re.tls=Re.ssl,Re.mqtts=ea().default);Re.ws=ca().default;Re.wss=ca().default;function hS(t){let e;t.auth&&(e=t.auth.match(/^(.+):(.+)$/),e?(t.username=e[1],t.password=e[2]):t.username=t.auth)}function ag(t,e){if(lg("connecting to an MQTT broker..."),typeof t=="object"&&!e&&(e=t,t=""),e=e||{},t&&typeof t=="string"){let n=uS.default.parse(t,!0);if(n.port!=null&&(n.port=Number(n.port)),e=Object.assign(Object.assign({},n),e),e.protocol===null)throw new Error("Missing protocol");e.protocol=e.protocol.replace(/:$/,"")}if(hS(e),e.query&&typeof e.query.clientId=="string"&&(e.clientId=e.query.clientId),e.cert&&e.key)if(e.protocol){if(["mqtts","wss","wxs","alis"].indexOf(e.protocol)===-1)switch(e.protocol){case"mqtt":e.protocol="mqtts";break;case"ws":e.protocol="wss";break;case"wx":e.protocol="wxs";break;case"ali":e.protocol="alis";break;default:throw new Error(`Unknown protocol for secure connection: "${e.protocol}"!`)}}else throw new Error("Missing secure protocol key");if(!Re[e.protocol]){let n=["mqtts","wss"].indexOf(e.protocol)!==-1;e.protocol=["mqtt","mqtts","ws","wss","wx","wxs","ali","alis"].filter((o,s)=>n&&s%2===0?!1:typeof Re[o]=="function")[0]}if(e.clean===!1&&!e.clientId)throw new Error("Missing clientId for unclean clients");e.protocol&&(e.defaultProtocol=e.protocol);function r(n){return e.servers&&((!n._reconnectCount||n._reconnectCount===e.servers.length)&&(n._reconnectCount=0),e.host=e.servers[n._reconnectCount].host,e.port=e.servers[n._reconnectCount].port,e.protocol=e.servers[n._reconnectCount].protocol?e.servers[n._reconnectCount].protocol:e.defaultProtocol,e.hostname=e.host,n._reconnectCount++),lg("calling streambuilder for",e.protocol),Re[e.protocol](n,e)}let i=new fS.default(r,e);return i.on("error",()=>{}),i}function dS(t,e,r=!0){return new Promise((i,n)=>{let o=ag(t,e),s={connect:u=>{l(),i(o)},end:()=>{l(),i(o)},error:u=>{l(),o.end(),n(u)}};r===!1&&(s.close=()=>{s.error(new Error("Couldn't connect to server"))});function l(){Object.keys(s).forEach(u=>{o.off(u,s[u])})}Object.keys(s).forEach(u=>{o.on(u,s[u])})})}Sr.connectAsync=dS;Sr.default=ag});var ha=L(K=>{"use strict";_();E();m();var fg=K&&K.__createBinding||(Object.create?function(t,e,r,i){i===void 0&&(i=r);var n=Object.getOwnPropertyDescriptor(e,r);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,i,n)}:function(t,e,r,i){i===void 0&&(i=r),t[i]=e[r]}),pS=K&&K.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),gS=K&&K.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var r in t)r!=="default"&&Object.prototype.hasOwnProperty.call(t,r)&&fg(e,t,r);return pS(e,t),e},cg=K&&K.__exportStar||function(t,e){for(var r in t)r!=="default"&&!Object.prototype.hasOwnProperty.call(e,r)&&fg(e,t,r)},es=K&&K.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(K,"__esModule",{value:!0});K.ReasonCodes=K.UniqueMessageIdProvider=K.DefaultMessageIdProvider=K.Store=K.MqttClient=K.connectAsync=K.connect=K.Client=void 0;var hg=es(Gn());K.MqttClient=hg.default;var yS=es(Wo());K.DefaultMessageIdProvider=yS.default;var bS=es(Sp());K.UniqueMessageIdProvider=bS.default;var wS=es(Vo());K.Store=wS.default;var dg=gS(ug());K.connect=dg.default;Object.defineProperty(K,"connectAsync",{enumerable:!0,get:function(){return dg.connectAsync}});K.Client=hg.default;cg(Gn(),K);cg(Ei(),K);var _S=vi();Object.defineProperty(K,"ReasonCodes",{enumerable:!0,get:function(){return _S.ReasonCodes}})});var AS=L(We=>{_();E();m();var pg=We&&We.__createBinding||(Object.create?function(t,e,r,i){i===void 0&&(i=r);var n=Object.getOwnPropertyDescriptor(e,r);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,i,n)}:function(t,e,r,i){i===void 0&&(i=r),t[i]=e[r]}),mS=We&&We.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),ES=We&&We.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var r in t)r!=="default"&&Object.prototype.hasOwnProperty.call(t,r)&&pg(e,t,r);return mS(e,t),e},vS=We&&We.__exportStar||function(t,e){for(var r in t)r!=="default"&&!Object.prototype.hasOwnProperty.call(e,r)&&pg(e,t,r)};Object.defineProperty(We,"__esModule",{value:!0});var SS=ES(ha());We.default=SS;vS(ha(),We)});return AS();})();
/*! Bundled license information:

@jspm/core/nodelibs/browser/buffer.js:
  (*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> *)
*/
