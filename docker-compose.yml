services:
  mosquitto:
    image: eclipse-mosquitto:2.0
    container_name: mosquitto
    ports:
      - "1883:1883"
      - "9002:9002"
    volumes:
      - ./mosquitto.conf:/mosquitto/config/mosquitto.conf
      - ./ssl:/mosquitto/config/ssl:ro
    networks:
      - webrtc-network

  webrtc-app:
    build: .
    container_name: webrtc-app
    ports:
      - "443:8443"
    depends_on:
      - mosquitto
    environment:
      - MQTT_BROKER=mosquitto
      - MQTT_PORT=1883
      - PORT=8443
    volumes:
      - ./ssl:/app/ssl:ro
    networks:
      - webrtc-network

networks:
  webrtc-network:
    driver: bridge
