<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <style>
      .webrtc-icon { fill: #2196F3; }
      .video-icon { fill: #4CAF50; }
      .connection { fill: #FF9800; }
    </style>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="#1a1a1a" stroke="#2196F3" stroke-width="2"/>
  
  <!-- Video camera icon -->
  <rect x="6" y="10" width="12" height="8" rx="2" class="video-icon"/>
  <rect x="8" y="12" width="8" height="4" fill="#1a1a1a"/>
  
  <!-- Lens -->
  <circle cx="12" cy="14" r="2" fill="#1a1a1a"/>
  <circle cx="12" cy="14" r="1" class="video-icon"/>
  
  <!-- Connection waves -->
  <path d="M20 12 Q24 14 24 16 Q24 18 20 20" stroke="#FF9800" stroke-width="2" fill="none"/>
  <path d="M22 13 Q25 14.5 25 16 Q25 17.5 22 19" stroke="#FF9800" stroke-width="1.5" fill="none"/>
  
  <!-- WebRTC text (simplified) -->
  <text x="16" y="26" text-anchor="middle" font-family="Arial, sans-serif" font-size="6" fill="#2196F3" font-weight="bold">RTC</text>
</svg>
