class WebRTCApp {
    constructor() {
        this.peer = null;
        this.mqttClient = null;
        this.roomName = null;
        this.userName = null;
        this.localStream = null;
        this.isInitiator = false;
        this.isConnecting = false;
        this.hasReceivedOffer = false;

        this.init();
    }

    init() {
        this.parseUrlParams();
        this.updateUI();
        this.setupMQTT();
        this.setupMedia();
        this.setupUnloadHandlers();
    }

    parseUrlParams() {
        const path = window.location.pathname;
        const urlParams = new URLSearchParams(window.location.search);

        // Try to get from URL path first: /room/roomName/userName
        const pathParts = path.split('/').filter(part => part);
        if (pathParts.length >= 3 && pathParts[0] === 'room') {
            this.roomName = pathParts[1];
            this.userName = pathParts[2];
        } else {
            // Fallback to URL parameters
            this.roomName = urlParams.get('room') || 'default-room';
            this.userName = urlParams.get('user') || `user-${Date.now()}`;
        }
    }

    updateUI() {
        document.getElementById('room-name').textContent = this.roomName;
        document.getElementById('user-name').textContent = this.userName;
    }

    setupMQTT() {
        // Use WSS for secure connection with faster keepalive
        const mqttUrl = `wss://${window.location.hostname}:9002`;

        console.log('Connecting to MQTT broker (WSS):', mqttUrl);
        this.mqttClient = mqtt.connect(mqttUrl, {
            keepalive: 10,          // Send ping every 10 seconds
            connectTimeout: 5000,   // 5 second connection timeout
            reconnectPeriod: 1000,  // Reconnect every 1 second if disconnected
            clean: true,            // Clean session
            clientId: `webrtc_${this.userName}_${Date.now()}`
        });

        this.mqttClient.on('connect', () => {
            console.log('MQTT connected');
            this.updateStatus('mqtt', 'connected');

            // Subscribe to room topic
            this.mqttClient.subscribe(`webrtc/${this.roomName}/signaling`);
            this.mqttClient.subscribe(`webrtc/${this.roomName}/presence`);

            // Announce presence
            this.mqttClient.publish(`webrtc/${this.roomName}/presence`, JSON.stringify({
                type: 'join',
                user: this.userName,
                timestamp: Date.now()
            }));

            // MQTT connected - status shown via icon color
        });

        this.mqttClient.on('message', (topic, message) => {
            try {
                const data = JSON.parse(message.toString());
                this.handleMQTTMessage(topic, data);
            } catch (error) {
                console.error('Error parsing MQTT message:', error);
            }
        });

        this.mqttClient.on('error', (error) => {
            console.error('MQTT error:', error);
            this.updateStatus('mqtt', 'disconnected');
        });

        this.mqttClient.on('disconnect', () => {
            console.log('MQTT disconnected');
            this.updateStatus('mqtt', 'disconnected');
            this.clearRemoteVideo();
            this.resetConnection();
        });
    }

    handleMQTTMessage(topic, data) {
        if (topic === `webrtc/${this.roomName}/presence`) {
            if (data.type === 'join' && data.user !== this.userName) {
                console.log('Another user joined:', data.user);
                // Only create peer if we don't have one and we're not already connecting
                if ((!this.peer || this.peer.destroyed) && !this.isConnecting) {
                    this.isInitiator = true;
                    this.isConnecting = true;
                    this.createPeer(true);
                }
            } else if (data.type === 'leave' && data.user !== this.userName) {
                console.log('User left:', data.user);
                // Immediately reset connection when peer leaves
                this.clearRemoteVideo();
                this.resetConnection();
            }
        } else if (topic === `webrtc/${this.roomName}/signaling`) {
            // Only process signaling messages not from ourselves
            if (data.from !== this.userName && (data.target === this.userName || !data.target)) {
                this.handleSignalingMessage(data);
            }
        }
    }

    handleSignalingMessage(data) {
        console.log('Received signaling message:', data.type, 'from:', data.from);

        if (data.type === 'offer') {
            // Only handle offer if we haven't received one already and we're not the initiator
            if (!this.hasReceivedOffer && !this.isInitiator) {
                this.hasReceivedOffer = true;
                this.isConnecting = true;

                if (!this.peer || this.peer.destroyed) {
                    this.createPeer(false);
                }

                if (this.peer && !this.peer.destroyed) {
                    try {
                        this.peer.signal(data.signal);
                    } catch (error) {
                        console.error('Error processing offer:', error);
                        this.resetConnection();
                    }
                }
            }
        } else if (data.type === 'answer') {
            // Only handle answer if we are the initiator and have a peer
            if (this.isInitiator && this.peer && !this.peer.destroyed) {
                try {
                    this.peer.signal(data.signal);
                } catch (error) {
                    console.error('Error processing answer:', error);
                    this.resetConnection();
                }
            }
        }
    }

    async setupMedia() {
        try {
            this.localStream = await navigator.mediaDevices.getUserMedia({
                video: true,
                audio: true
            });

            document.getElementById('localVideo').srcObject = this.localStream;
            this.updateStatus('video', 'Camera ready');
            this.hideRemoteVideo(); // Initialize with local video fullscreen
        } catch (error) {
            console.error('Error accessing media devices:', error);
            this.updateStatus('video', 'Camera error');
        }
    }

    createPeer(initiator) {
        // Clean up existing peer if it exists
        if (this.peer && !this.peer.destroyed) {
            this.peer.destroy();
        }

        this.peer = new SimplePeer({
            initiator: initiator,
            stream: this.localStream,
            trickle: false
        });

        this.peer.on('signal', (data) => {
            const message = {
                type: initiator ? 'offer' : 'answer',
                signal: data,
                from: this.userName,
                timestamp: Date.now()
            };

            this.mqttClient.publish(`webrtc/${this.roomName}/signaling`, JSON.stringify(message));
        });

        this.peer.on('connect', () => {
            console.log('Peer connected');
            this.updateStatus('peer', 'connected');
            this.isConnecting = false;
        });

        this.peer.on('stream', (stream) => {
            console.log('Received remote stream');
            document.getElementById('remoteVideo').srcObject = stream;
            this.showRemoteVideo();
        });

        this.peer.on('error', (error) => {
            console.error('Peer error:', error);
            this.updateStatus('peer', 'disconnected');
            this.clearRemoteVideo();
            this.resetConnection();
        });

        this.peer.on('close', () => {
            console.log('Peer connection closed');
            this.updateStatus('peer', 'disconnected');
            this.clearRemoteVideo();
            this.resetConnection();
        });

        // Monitor ICE connection state for faster disconnection detection
        this.peer._pc.addEventListener('iceconnectionstatechange', () => {
            const state = this.peer._pc.iceConnectionState;
            console.log('ICE connection state:', state);

            if (state === 'disconnected' || state === 'failed' || state === 'closed') {
                console.log('ICE connection lost, resetting...');
                this.updateStatus('peer', 'disconnected');
                this.clearRemoteVideo();
                this.resetConnection();
            }
        });
    }

    resetConnection() {
        console.log('Resetting connection');
        this.isConnecting = false;
        this.hasReceivedOffer = false;
        this.isInitiator = false;

        if (this.peer && !this.peer.destroyed) {
            this.peer.destroy();
        }
        this.peer = null;

        // Clear remote video when resetting
        this.clearRemoteVideo();
    }

    clearRemoteVideo() {
        const remoteVideo = document.getElementById('remoteVideo');
        if (remoteVideo.srcObject) {
            // Stop all tracks in the remote stream
            const tracks = remoteVideo.srcObject.getTracks();
            tracks.forEach(track => track.stop());

            // Clear the video source
            remoteVideo.srcObject = null;
            console.log('Remote video cleared');
        }
        this.hideRemoteVideo();
    }

    showRemoteVideo() {
        const remoteVideo = document.getElementById('remoteVideo');
        const localVideo = document.getElementById('localVideo');

        // Show remote video in fullscreen
        remoteVideo.classList.remove('hidden');

        // Move local video to PIP mode (top-right)
        localVideo.className = 'fixed top-6 right-6 w-80 h-48 rounded-xl border-4 border-green-500 shadow-2xl object-cover transition-all duration-500 ease-in-out z-30';
    }

    hideRemoteVideo() {
        const remoteVideo = document.getElementById('remoteVideo');
        const localVideo = document.getElementById('localVideo');

        // Hide remote video
        remoteVideo.classList.add('hidden');

        // Return local video to fullscreen mode
        localVideo.className = 'fixed inset-0 w-full h-full object-cover bg-gray-900 z-20';
    }

    addStatusPulse() {
        // No longer needed - status is icon-only
    }

    removeStatusPulse() {
        // No longer needed - status is icon-only
    }

    updateStatus(type, status) {
        if (type === 'mqtt') {
            const icon = document.getElementById('mqtt-icon');
            const tooltip = document.getElementById('mqtt-tooltip-text');
            icon.setAttribute('class', this.getIconColorClass(status));
            tooltip.textContent = this.getMQTTTooltipText(status);
        } else if (type === 'peer') {
            const icon = document.getElementById('peer-icon');
            const tooltip = document.getElementById('peer-tooltip-text');
            icon.setAttribute('class', this.getIconColorClass(status));
            tooltip.textContent = this.getPeerTooltipText(status);
        } else if (type === 'video') {
            const icon = document.getElementById('video-icon');
            const tooltip = document.getElementById('video-tooltip-text');
            icon.setAttribute('class', this.getVideoIconColorClass(status));
            tooltip.textContent = this.getVideoTooltipText(status);
        }
    }

    getIconColorClass(status) {
        const baseClasses = 'w-5 h-5 fill-current cursor-help';
        switch (status) {
            case 'connected':
                return `${baseClasses} text-white`;
            case 'disconnected':
                return `${baseClasses} text-red-400`;
            case 'connecting':
                return `${baseClasses} text-orange-400`;
            default:
                return `${baseClasses} text-gray-400`;
        }
    }

    getVideoIconColorClass(status) {
        const baseClasses = 'w-5 h-5 fill-current cursor-help';
        if (status.includes('ready') || status.includes('Ready')) {
            return `${baseClasses} text-white`;
        } else if (status.includes('error') || status.includes('Error')) {
            return `${baseClasses} text-red-400`;
        } else {
            return `${baseClasses} text-yellow-400`;
        }
    }

    getMQTTTooltipText(status) {
        switch (status) {
            case 'connected':
                return 'MQTT Broker: Connected - Signaling ready';
            case 'disconnected':
                return 'MQTT Broker: Disconnected - Cannot communicate';
            case 'connecting':
                return 'MQTT Broker: Connecting - Establishing connection';
            default:
                return 'MQTT Broker: Unknown status';
        }
    }

    getPeerTooltipText(status) {
        switch (status) {
            case 'connected':
                return 'WebRTC Connection: Connected - Video call active';
            case 'disconnected':
                return 'WebRTC Connection: Disconnected - Waiting for peer';
            case 'connecting':
                return 'WebRTC Connection: Connecting - Establishing peer link';
            default:
                return 'WebRTC Connection: Unknown status';
        }
    }

    getVideoTooltipText(status) {
        if (status.includes('ready') || status.includes('Ready')) {
            return 'Camera & Microphone: Ready - Media devices active';
        } else if (status.includes('error') || status.includes('Error')) {
            return 'Camera & Microphone: Error - Media access denied';
        } else if (status.includes('Initializing') || status.includes('initializing')) {
            return 'Camera & Microphone: Initializing - Requesting access';
        } else {
            return `Camera & Microphone: ${status}`;
        }
    }

    setupUnloadHandlers() {
        // Send leave message when user closes tab/browser
        const sendLeaveMessage = () => {
            if (this.mqttClient && this.mqttClient.connected) {
                this.mqttClient.publish(`webrtc/${this.roomName}/presence`, JSON.stringify({
                    type: 'leave',
                    user: this.userName,
                    timestamp: Date.now()
                }));
            }
        };

        // Handle page unload
        window.addEventListener('beforeunload', sendLeaveMessage);
        window.addEventListener('unload', sendLeaveMessage);

        // Handle visibility change (tab switching)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // Tab is hidden, but don't send leave message yet
                console.log('Tab hidden');
            } else {
                // Tab is visible again
                console.log('Tab visible');
            }
        });
    }
}

// Initialize the app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new WebRTCApp();
});
