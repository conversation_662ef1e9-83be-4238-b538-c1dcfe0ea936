<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC Simple Peer</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <script src="/assets/tailwind.min.js"></script>
    <script src="/assets/flowbite.min.js"></script>
    <link href="/assets/flowbite.min.css" rel="stylesheet" />
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'pulse-slow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    }
                }
            }
        }
    </script>
</head>
<body class="h-full bg-gray-900 overflow-hidden">
    <div id="app" class="relative h-screen w-screen">
        <!-- Remote video (fullscreen when connected) -->
        <video id="remoteVideo" autoplay playsinline
               class="hidden fixed inset-0 w-full h-full object-cover bg-gray-900 z-10"></video>

        <!-- Local video (fullscreen when waiting, PIP when connected) -->
        <video id="localVideo" autoplay muted playsinline
               class="fixed inset-0 w-full h-full object-cover bg-gray-900 z-20"></video>

        <!-- Room info overlay - top left -->
        <div id="room-info" class="fixed top-4 left-4 z-50 backdrop-blur-md bg-black/60 border border-white/10 rounded-lg px-3 py-2 shadow-xl">
            <div class="flex items-center space-x-4 text-white text-sm">
                <div class="flex items-center space-x-1">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                    </svg>
                    <span id="room-name" class="font-medium">Loading...</span>
                </div>
                <div class="flex items-center space-x-1">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                    </svg>
                    <span id="user-name" class="font-medium">Loading...</span>
                </div>
            </div>
        </div>

        <!-- Status panel - bottom right -->
        <div id="status-panel" class="fixed bottom-4 right-4 z-50 backdrop-blur-md bg-black/60 border border-white/10 rounded-lg px-3 py-2 shadow-xl">
            <!-- Status icons only -->
            <div class="flex items-center justify-center space-x-3">
                <!-- MQTT Status -->
                <div data-tooltip-target="mqtt-tooltip" data-tooltip-placement="top">
                    <svg id="mqtt-icon" class="w-5 h-5 text-red-400 cursor-help" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"/>
                    </svg>
                </div>
                <div id="mqtt-tooltip" role="tooltip" class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                    <span id="mqtt-tooltip-text">MQTT Broker: Disconnected</span>
                    <div class="tooltip-arrow" data-popper-arrow></div>
                </div>

                <!-- Peer Status -->
                <div data-tooltip-target="peer-tooltip" data-tooltip-placement="top">
                    <svg id="peer-icon" class="w-5 h-5 text-red-400 cursor-help" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div id="peer-tooltip" role="tooltip" class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                    <span id="peer-tooltip-text">WebRTC Connection: Disconnected</span>
                    <div class="tooltip-arrow" data-popper-arrow></div>
                </div>

                <!-- Video Status -->
                <div data-tooltip-target="video-tooltip" data-tooltip-placement="top">
                    <svg id="video-icon" class="w-5 h-5 text-yellow-400 cursor-help" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                    </svg>
                </div>
                <div id="video-tooltip" role="tooltip" class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                    <span id="video-tooltip-text">Camera & Microphone: Initializing...</span>
                    <div class="tooltip-arrow" data-popper-arrow></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/assets/mqtt.min.js"></script>
    <script src="/assets/simple-peer.min.js"></script>
    <script src="/app.js"></script>
</body>
</html>
